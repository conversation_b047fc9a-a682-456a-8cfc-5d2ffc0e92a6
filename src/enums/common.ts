import { useI18n } from '../hooks/web/useI18n';

const t = useI18n('common').t;

/** 表单页类型枚举 */
export const PageTypeEnum = {
  VIEW: 'view',
  EDIT: 'edit',
  ADD: 'add',
  AssignPermissions: 'assignPermissions',
  ViewAssignPermissions: 'viewAssignPermissions',
} as const;

/** 操作类型枚举 */
export const ActionTypeEnum = {
  ...PageTypeEnum,
  DELETE: 'delete',
} as const;

export type PageTypeUnion = (typeof PageTypeEnum)[keyof typeof PageTypeEnum];

export type AuthTypeUnion = 'VIEW' | 'EDIT' | 'ADD' | 'DELETE' | 'EXPORT' | 'CANCEL' | 'SEND_LIST' | 'UPDATE_STATUS';

export type ActionTypeUnion = (typeof ActionTypeEnum)[keyof typeof ActionTypeEnum];

/** 文件上传状态枚举值 */
export enum UploadStatusEnum {
  /** 初始化 */
  INIT = 0,
  /** 上传中 */
  UPLOADING = 1,
  /** 上传成功 */
  SUCCESS = 2,
  /** 上传失败 */
  FAIL = 3,
}

export enum EnableOrDisableStatusEnum {
  /** 禁用 */
  DISABLE = 0,
  /** 启用 */
  ENABLE = 1,
}

export const EnableOrDisableStatusOptions = [
  {
    label: t('enableText'),
    value: EnableOrDisableStatusEnum.ENABLE,
  },
  {
    label: t('disableText'),
    value: EnableOrDisableStatusEnum.DISABLE,
  },
];

export enum IsModifyStatusEnum {
  /** 是 */
  YES = 1,
  /** 否 */
  NO = 0,
}

export const IsModifyStatusOptions = [
  {
    label: t('correct'),
    value: IsModifyStatusEnum.YES,
  },
  {
    label: t('deny'),
    value: IsModifyStatusEnum.NO,
  },
];

/** 定时任务重复周期类型 */
export enum REPEAT_TYPE_ENUM {
  NO_REPEAT = 'noRepeat',
  REPEAT = 'repeat',
}

/** 是否重复任务 */
export enum IsPeriodicEnum {
  ONE_TIME = 0, // 一次性任务
  PERIODIC = 1, // 重复任务
}

/** 账户类型枚举值 */
export enum AccountTypeEnum {
  /** 厂端 */
  FACTORY_END = '1',
  /** 总代&子公司 */
  GENERAL_AGENT_SUBSIDIARY = '2',
  /** 网点 */
  NETWORK = '3',
}
