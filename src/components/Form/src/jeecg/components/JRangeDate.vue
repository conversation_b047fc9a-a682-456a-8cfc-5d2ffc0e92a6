<template>
  <AToolTip :title="tooltipContent" placement="top" :mouse-enter-delay="0.3" :mouse-leave-delay="0.1">
    <a-range-picker v-model:value="rangeValue" :valueFormat="valueFormat" @change="handleChange" :show-time="datetime" />
  </AToolTip>
</template>

<script lang="ts">
  import { defineComponent, ref, watch, computed } from 'vue';
  import { propTypes } from '/@/utils/propTypes';
  import { Form, Tooltip as AToolTip } from 'ant-design-vue';

  /**
   * 用于范围查询
   */
  export default defineComponent({
    name: 'JRangeDate',
    components: {
      AToolTip,
    },
    props: {
      value: propTypes.string.def(''),
      datetime: propTypes.bool.def(true),
      placeholder: propTypes.string.def(''),
      showTooltip: propTypes.bool.def(false),
    },
    emits: ['change', 'update:value'],
    setup(props, { emit }) {
      const rangeValue = ref([] as string[]);
      const formItemContext = Form.useInjectFormItemContext();

      watch(
        () => props.value,
        (val) => {
          if (val) {
            rangeValue.value = val.split(',');
          } else {
            rangeValue.value = [];
          }
        },
        { immediate: true }
      );

      const valueFormat = computed(() => {
        if (props.datetime === true) {
          return 'YYYY-MM-DD HH:mm:ss';
        } else {
          return 'YYYY-MM-DD';
        }
      });

      function handleChange(arr) {
        let str = '';
        if (arr && arr.length > 0) {
          // update-begin--author:liaozhiyang---date:20240710---for：[issues/6368] rangeDate去掉判断允许起始项或结束项为空兼容allowEmpty
          str = arr.join(',');
          // update-end--author:liaozhiyang---date:20240710---for：[issues/6368] rangeDate去掉判断允许起始项或结束项为空兼容allowEmpty
        }
        emit('change', str);
        emit('update:value', str);
        formItemContext.onFieldChange();
      }

      /**
       * 计算tooltip内容
       */
      const tooltipContent = computed(() => {
        console.log('我触发了吗', rangeValue.value);
        return '我是tooltip';
      });

      /**
       * 获取弹出容器
       */
      const getPopupContainer = (triggerNode: HTMLElement) => {
        // 优先使用最近的滚动容器，避免tooltip被遮挡
        return triggerNode.parentElement || document.body;
      };

      return {
        rangeValue,
        valueFormat,
        handleChange,
        tooltipContent,
        getPopupContainer,
      };
    },
  });
</script>

<style scoped></style>
