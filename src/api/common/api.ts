import { defHttp } from '/@/utils/http/axios';
import { useI18n } from '/@/hooks/web/useI18n';
import { useMessage } from '/@/hooks/web/useMessage';
import { useUserStore } from '/@/store/modules/user';
import { useGlobSetting } from '/@/hooks/setting';

const { createMessage } = useMessage();
const { t } = useI18n();

const globSetting = useGlobSetting();
const baseUploadUrl = globSetting.uploadUrl;
enum Api {
  positionList = '/sys/position/list',
  userList = '/sys/user/list',
  roleList = '/sys/role/list',
  queryDepartTreeSync = '/sys/sysDepart/queryDepartTreeSync',
  queryTreeList = '/sys/sysDepart/queryTreeList',
  loadTreeData = '/sys/category/loadTreeData',
  loadDictItem = '/sys/category/loadDictItem/',
  getDictItems = '/sys/dict/getDictItems/',
  getTableList = '/sys/user/queryUserComponentData',
  getCategoryData = '/sys/category/loadAllData',
}

/**
 * 根据当前用户获取当前APP/官网跳转地址
 */
export const getWebconfigUrlListApi = () => {
  return defHttp.post({ url: '/webconfig/list' });
};

/**
 * 发送登录验证码
 */
export const sendLoginMsgApi = (params: {
  account: string; // 账号名
  language: string; // 语种
}) => {
  return defHttp.post({ url: '/sms/login/sendMsg', params });
};

/**
 * 发送登录验证码
 */
export const forgetSendSmsApi = (params: {
  account: string; // 账号名
}) => {
  return defHttp.post({ url: '/sms/forgot/send', params });
};

/**
 * 获取国际区号
 */
export const queryAreaListApi = () => {
  return defHttp.post({ url: '/queryAreaList' });
};

/**
 * 修改密码发送验证码
 */
export const resetPwdSendMsgApi = (params: { account?: string }) => {
  return defHttp.post({ url: '/sys/user/resetPwdSendMsg', params });
};

/**
 * 获取支持语言接口
 */
export const getLanguageTypeApi = (params) => {
  return defHttp.post({ url: '/language/v1/getLanguageType', params });
};

/**
 * 添加操作日志接口
 */
export const addLogInfoApi = (params: {
  bizId: string; // 数据业务id ,
  bizType: number; // 业务类型 1:角色管理 ,
  cnName: string; // 操作人名称 ,
  enName: string; // 操作人账户 ,
  logType: string; //  操作类型 中文：新增、编辑等
  remarker?: string; // 备注信息 ,
}) => {
  return defHttp.post({ url: '/action/log/addActionLog', params });
};

// 新增审计日志
export function addSjLogFn(remarker: string, methodStr = '导入') {
  const { userInfo } = useUserStore();
  addLogInfoApi({
    bizType: 0,
    bizId: userInfo?.userCode || '',
    cnName: userInfo?.username || '',
    enName: userInfo?.englishName || '',
    logType: methodStr,
    remarker: remarker,
  });
}

/**
 * 操作日志接口-存在参数查询
 */
export const queryLogInfoApi = (params) => {
  return defHttp.post({ url: '/action/log/queryLogInfo', params });
};

export const querySjLogExport = '/action/log/export/details';

/**
 * 操作日志接口
 */
export const getLogInfoApi = (params: {
  bizId: string; // 数据业务id ,
  bizType: number; // 业务类型 1:角色管理 ,
  pageIndex: number; //  当前页码 ,
  pageSize: number; // 每页查询数量
}) => {
  return defHttp.post({ url: '/action/log/logInfo', params });
};

/**
 * 上传父路径
 */
export const uploadUrl = `${baseUploadUrl}/sys/common/upload`;

/**
 * 职务列表
 * @param params
 */
export const getPositionList = (params) => {
  return defHttp.get({ url: Api.positionList, params });
};

/**
 * 用户列表
 * @param params
 */
export const getUserList = (params) => {
  return defHttp.get({ url: Api.userList, params });
};

/**
 * 角色列表
 * @param params
 */
export const getRoleList = (params) => {
  return defHttp.get({ url: Api.roleList, params });
};
/**
 * 系统角色列表
 * @param params
 */
export const featchRoleList = (params: { pageIndex: number; pageSize: number; roleId?: string; roleName?: string }) =>
  defHttp.post({ url: Api.roleList, params });
/**
 * 异步获取部门树列表
 */
export const queryDepartTreeSync = (params?) => {
  return defHttp.get({ url: Api.queryDepartTreeSync, params });
};
/**
 * 获取部门树列表
 */
export const queryTreeList = (params?) => {
  return defHttp.get({ url: Api.queryTreeList, params });
};

/**
 * 分类字典树控件 加载节点
 */
export const loadTreeData = (params?) => {
  return defHttp.get({ url: Api.loadTreeData, params });
};

/**
 * 根据字典code加载字典text
 */
export const loadDictItem = (params?) => {
  return defHttp.get({ url: Api.loadDictItem, params });
};

/**
 * 根据字典code加载字典text
 */
export const getDictItems = (dictCode) => {
  return defHttp.get({ url: Api.getDictItems + dictCode }, { joinTime: false });
};
/**
 * 部门用户modal选择列表加载list
 */
export const getTableList = (params) => {
  return defHttp.get({ url: Api.getTableList, params });
};
/**
 * 加载全部分类字典数据
 */
export const loadCategoryData = (params) => {
  return defHttp.get({ url: Api.getCategoryData, params });
};
/**
 * 文件上传
 */
export const uploadFile = (params, success) => {
  return defHttp.uploadFile({ url: uploadUrl }, params, { success });
};
/**
 * 下载文件
 * @param url 文件路径
 * @param fileName 文件名
 * @param parameter
 * @returns {*}
 */
export const downloadFile = (url, fileName?, parameter?) => {
  return getFileblob(url, parameter).then((data) => {
    if (!data || data.size === 0) {
      createMessage.warning(t('common.file_download_fail'));
      return;
    }
    // @ts-ignore
    if (typeof window.navigator.msSaveBlob !== 'undefined') {
      // @ts-ignore
      window.navigator.msSaveBlob(new Blob([data]), fileName);
    } else {
      const url = window.URL.createObjectURL(new Blob([data]));
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link); //下载完成移除元素
      window.URL.revokeObjectURL(url); //释放掉blob对象
    }
  });
};

/**
 * 下载文件 用于excel导出
 * @param url
 * @param parameter
 * @returns {*}
 */
export const getFileblob = (url, parameter) => {
  return defHttp.get(
    {
      url: url,
      params: parameter,
      responseType: 'blob',
    },
    { isTransformResponse: false }
  );
};

/**
 * 【用于评论功能】自定义文件上传-方法
 */
export const uploadMyFile = (url, data) => {
  return defHttp.uploadMyFile(url, data);
};

/**
 * 查询当前用户所有的数据权限（大区，国家，州/省，城市）
 */
export const queryAreaHaveAuth = (userId: string) => {
  return defHttp.post({ url: `/data/perm/queryAreaHaveAuth/${userId}` });
};

/**
 * 多语言资源类型
 * UNIFIED_FRONT_END：统一前端
 * clue-vue：线索vue
 * work-order-vue：工单vue
 */
export type multiLingualResourceType = 'UNIFIED_FRONT_END' | 'clue-vue' | 'work-order-vue';

/**
 * 获取多语言翻译包
 */
export const getMultiLingual = (params: { resourceType: multiLingualResourceType }) => {
  return defHttp.post({ url: `/language/v1/multiLingual/queryListByResourceType/${params.resourceType}` });
};
