/**
 * Tooltip配置接口
 */
export interface TooltipConfig {
  /** 是否显示百分比 */
  showPercentage?: boolean;
  /** 是否显示总计 */
  showTotal?: boolean;
  /** 需要排除的系列类型 */
  excludeSeriesTypes?: string[];
  /** 特殊系列配置 */
  specialSeries?: { type: string; label: string };
  /** 自定义额外信息提供者 */
  extraInfoProvider?: (axisValue: string, params: any[]) => string;
  /** 自定义值格式化器 */
  valueFormatter?: (value: number, percent?: number) => string;
  /** 总计标签 */
  totalLabel?: string;
}

/**
 * 统一的Tooltip格式化器工厂函数
 * 通过配置对象支持所有tooltip场景，消除代码重复
 */
export const createTooltipFormatter = (config: TooltipConfig = {}): ((params: any) => string) => {
  const {
    showPercentage = false,
    showTotal = false,
    excludeSeriesTypes = [],
    specialSeries = null,
    extraInfoProvider = null,
    valueFormatter = null,
    totalLabel = '总计',
  } = config;

  return (params: any): string => {
    // 兼容漏斗图：如果是单个对象，转换为数组格式
    if (!Array.isArray(params)) {
      if (!params || typeof params !== 'object') {
        return '';
      }
      // 漏斗图单项处理
      const itemName = params.name || params.seriesName || '';
      const itemValue = params.value || params.data?.value || 0;

      let tooltipContent = `<div style="margin-bottom: 4px; font-weight: bold;">${itemName}</div>`;

      if (valueFormatter) {
        const percent = params.data?.percent || params.percent || 0;
        tooltipContent += `<div style="margin: 2px 0;">${valueFormatter(itemValue, percent)}</div>`;
      } else {
        tooltipContent += `<div style="margin: 2px 0;">数量: ${itemValue}</div>`;
      }

      // 添加额外信息（漏斗图专用）
      if (extraInfoProvider) {
        const extraInfo = extraInfoProvider(itemName, [params]);
        if (extraInfo) {
          tooltipContent += `<div style="margin-top: 4px;">${extraInfo}</div>`;
        }
      }

      return tooltipContent;
    }

    // 多系列图表处理（原有逻辑）
    if (params.length === 0) {
      return '';
    }

    const axisValue = params[0].axisValue;
    let tooltipContent = `<div style="margin-bottom: 4px; font-weight: bold;">${axisValue}</div>`;

    // 过滤数据
    const filteredParams = excludeSeriesTypes.length > 0 ? params.filter((param: any) => !excludeSeriesTypes.includes(param.seriesType)) : params;

    const specialParam = specialSeries ? params.find((param: any) => param.seriesType === specialSeries.type) : null;

    // 显示总计
    if (showTotal) {
      const total = specialParam ? specialParam.data.value : filteredParams.reduce((sum: number, param: any) => sum + (param.data.value || 0), 0);
      tooltipContent += `<div style="margin-bottom: 4px;">${totalLabel}: ${total}</div>`;
    }

    // 显示常规数据
    filteredParams.forEach((param: any) => {
      const value = param.data.value || 0;
      let displayText = param.seriesName + ': ';

      if (valueFormatter) {
        // 使用自定义格式化器
        const percent = param.data.percent || 0;
        displayText += valueFormatter(value, percent);
      } else if (showPercentage) {
        // 计算并显示百分比
        const total =
          showTotal && specialParam ? specialParam.data.value : filteredParams.reduce((sum: number, p: any) => sum + (p.data.value || 0), 0);
        const percent = param.data.percent || (total > 0 ? calculatePercentage(value, total) : 0);
        displayText += formatValueWithPercentage(value, percent);
      } else {
        // 仅显示数值
        displayText += value.toString();
      }

      tooltipContent += `
        <div style="display: flex; align-items: center; margin: 2px 0;">
          <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>
          <span>${displayText}</span>
        </div>
      `;
    });

    // 显示特殊系列数据
    if (specialParam && specialSeries) {
      tooltipContent += `
        <div style="display: flex; align-items: center; margin: 2px 0;">
          <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${specialParam.color};"></span>
          <span>${specialParam.seriesName}: ${specialParam.data.value}</span>
        </div>
      `;
    }

    // 添加额外信息
    if (extraInfoProvider) {
      const extraInfo = extraInfoProvider(axisValue, params);
      if (extraInfo) {
        tooltipContent += `<div style="margin-top: 4px;">${extraInfo}</div>`;
      }
    }

    return tooltipContent;
  };
};

/**
 * 通用的百分比计算函数
 */
export const calculatePercentage = (value: number, total: number): string => {
  return total > 0 ? ((value / total) * 100).toFixed(1) : '0.0';
};

/**
 * 通用的标签和tooltip格式化函数
 */
export const formatValueWithPercentage = (value: number, percent: string | number): string => {
  return `${value} (${percent}%)`;
};

/**
 * 标准化尺寸值 - 支持数字、百分比、视口单位等
 * @param value 尺寸值，可以是数字或字符串
 * @returns 标准化的CSS尺寸字符串
 */
export const normalizeSizeValue = (value: number | string | undefined): string | undefined => {
  if (value === undefined || value === null) {
    return undefined;
  }

  // 如果是数字，添加px单位
  if (typeof value === 'number') {
    return `${value}px`;
  }

  // 如果是字符串，直接返回（支持百分比、vh、vw等单位）
  if (typeof value === 'string') {
    return value;
  }

  return undefined;
};

/**
 * 生成图表数据项（包含百分比）
 */
export const generateChartDataItem = (name: string, value: number, percent: string, additionalProps: Record<string, any> = {}): any => {
  return {
    name,
    value: Number(value) || 0,
    percent: parseFloat(percent),
    ...additionalProps,
  };
};
