import dayjs from 'dayjs';
import { useI18n } from '/@/hooks/web/useI18n';
import { useUserStore } from '/@/store/modules/user';
import { StatisticalDimensionTypeEnum } from '../enums';
import type { commonQueryParams } from '../api';
import { isString } from 'lodash-es';

// 常量定义
const DATE_FORMAT = 'YYYY-MM-DD';
const DATE_SEPARATOR = ',';
const DEFAULT_PERIOD = 'day';
const DEFAULT_DAYS_SUBTRACT = 6;

// 统计维度映射表（使用枚举值）
const STATISTICAL_DIMENSION_MAP: Record<string, StatisticalDimensionTypeEnum> = {
  day: StatisticalDimensionTypeEnum.DAY,
  week: StatisticalDimensionTypeEnum.WEEK,
  month: StatisticalDimensionTypeEnum.MONTH,
  season: StatisticalDimensionTypeEnum.SEASON,
  year: StatisticalDimensionTypeEnum.YEAR,
};

// 地区&经销商&网点参数字段映射
export const REGION_PARAM_FIELDS = ['regionCode', 'countryCode', 'provinceCode', 'cityCode', 'dealerCode', 'networkCode'] as const;

/**
 * 获取日期范围校验配置
 * @returns 日期范围校验配置对象
 */
export const getDateValidationConfig = () => {
  const { t } = useI18n('common');

  return {
    day: { maxRange: 1, unit: 'month', label: `1${t('months')}` },
    week: { maxRange: 10, unit: 'week', label: `10${t('week')}` },
    month: { maxRange: 12, unit: 'month', label: `12${t('months')}` },
    season: { maxRange: 10, unit: 'quarter', label: `10${t('quarter')}` },
    year: { maxRange: 5, unit: 'year', label: `${t('year')}` },
  } as const;
};

/**
 * 校验日期范围是否符合业务规则
 * @param values 表单值
 * @returns 校验结果
 */
export const validateDateRange = (values: Record<string, any>) => {
  const { t } = useI18n('common');
  const { statPeriod = 'day' } = values;

  let periodRange = values.periodRange;

  // 检查是否选择了日期范围
  if (!periodRange) {
    return { valid: false, message: t('pleaseSelectDateRange') };
  }

  if (isString(periodRange)) {
    periodRange = periodRange.split(DATE_SEPARATOR).map((date) => date.trim());
  }

  const [startDate, endDate] = periodRange;
  if (!startDate || !endDate) {
    return { valid: false, message: t('pleaseSelectDateRange') };
  }

  const start = dayjs(startDate);
  const end = dayjs(endDate);
  const now = dayjs();

  // 检查日期顺序
  if (start.isAfter(end)) {
    return { valid: false, message: t('startDateCannotAfterEndDate') };
  }

  // 检查是否选择了未来日期
  if (end.isAfter(now, 'day')) {
    return { valid: false, message: t('cannotSelectFutureDate') };
  }

  // 获取当前统计周期的限制配置
  const config = getDateValidationConfig()[statPeriod as keyof ReturnType<typeof getDateValidationConfig>];
  if (!config) {
    return { valid: true, message: '' };
  }

  // 计算时间跨度
  let timeDiff: number;
  if (statPeriod === 'season') {
    // 季度特殊处理：计算季度差
    timeDiff = Math.abs(end.diff(start, 'quarter', true));
  } else {
    // 其他周期按配置的单位计算
    timeDiff = Math.abs(end.diff(start, config.unit as any, true));
  }

  const messageMap = {
    day: t('months'),
    week: t('week'),
    month: t('months'),
    season: t('quarter'),
    year: t('year'),
  };

  // 检查时间跨度是否超出限制
  if (timeDiff > config.maxRange) {
    return {
      valid: false,
      message: `${t('theDateSpanCannotExceed', { message: `${config.maxRange}${messageMap[statPeriod]}` })}`,
    };
  }

  return { valid: true, message: '' };
};

// 日期范围解析配置映射
const DATE_RANGE_PARSERS = {
  // 字符串分隔符格式："2024-01-01,2024-01-31"
  stringSeparator: {
    condition: (value: any) => typeof value === 'string' && value.includes(DATE_SEPARATOR),
    handler: (value: string) => {
      const dates = value.split(DATE_SEPARATOR).map((date) => date.trim());
      if (dates.length === 2) {
        return { startDate: dates[0], endDate: dates[1] };
      }
      return {};
    },
  },
  // 单个字符串格式："2024-01-01"
  singleString: {
    condition: (value: any) => typeof value === 'string' && !value.includes(DATE_SEPARATOR) && value.trim(),
    handler: (value: string) => {
      const trimmedDate = value.trim();
      return { startDate: trimmedDate, endDate: trimmedDate };
    },
  },
  // 数组格式：["2024-01-01", "2024-01-31"]
  array: {
    condition: (value: any) => Array.isArray(value) && value.length === 2,
    handler: (value: string[], statPeriod: string) => {
      let [startDate, endDate] = value;

      // 季度特殊处理：转换为完整季度范围
      if (statPeriod === 'season' && startDate && endDate) {
        const start = dayjs(startDate);
        const end = dayjs(endDate);
        startDate = start.startOf('quarter').format(DATE_FORMAT);
        endDate = end.endOf('quarter').format(DATE_FORMAT);
      }

      return { startDate, endDate };
    },
  },
  // 默认处理（空值处理）
  default: {
    condition: () => true,
    handler: () => ({}),
  },
};

/**
 * 根据配置解析日期范围
 * @param periodRange 日期范围
 * @param statPeriod 统计周期
 * @returns 解析后的开始和结束日期
 */
const parseByConfig = (periodRange: any, statPeriod: string): { startDate?: string; endDate?: string } => {
  for (const parser of Object.values(DATE_RANGE_PARSERS)) {
    if (parser.condition(periodRange)) {
      return parser.handler(periodRange, statPeriod);
    }
  }
  return DATE_RANGE_PARSERS.default.handler();
};

/**
 * 解析日期范围的多种格式
 * @param periodRange 日期范围（字符串或数组格式）
 * @param statPeriod 统计周期
 * @returns 解析后的开始和结束日期
 */
export const parsePeriodRange = (periodRange: any, statPeriod: string): { startDate?: string; endDate?: string } => {
  return parseByConfig(periodRange, statPeriod);
};

/**
 * 格式化日期，确保统一格式
 * @param date 日期字符串或对象
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date: string | undefined): string | undefined => {
  if (!date) return undefined;

  // 如果是字符串，去除时分秒部分
  if (typeof date === 'string') {
    return date.split(' ')[0];
  }

  // 如果是 dayjs 对象，格式化为日期字符串
  return dayjs(date).format(DATE_FORMAT);
};

/**
 * 获取默认日期范围
 * @returns 默认的开始和结束日期
 */
export const getDefaultDateRange = () => ({
  startDate: dayjs().subtract(DEFAULT_DAYS_SUBTRACT, 'day').format(DATE_FORMAT),
  endDate: dayjs().format(DATE_FORMAT),
});

/**
 * 添加地区相关参数到查询对象
 * @param queryParams 查询参数对象
 * @param values 表单值
 */
const addRegionParams = (queryParams: Record<string, any>, values: Record<string, any>) => {
  REGION_PARAM_FIELDS.forEach((field) => {
    if (values[field]) {
      queryParams[field] = values[field];
    }
  });
};

/**
 * 转换表单值为查询参数格式
 * @param values 表单值
 * @param adjustDateRangeByPeriod 日期范围调整函数
 * @returns 转换后的查询参数
 */
export const transformValuesToQueryParams = (
  values: Record<string, any>,
  adjustDateRangeByPeriod?: (startDate: string, endDate: string, statPeriod: string) => { startDate: string; endDate: string }
): commonQueryParams => {
  const { userInfo } = useUserStore();
  const { statPeriod = DEFAULT_PERIOD, periodRange, ...regionParams } = values;

  // 解析日期范围
  const { startDate, endDate } = parsePeriodRange(periodRange, statPeriod);

  // 格式化日期
  const formattedStartDate = formatDate(startDate);
  const formattedEndDate = formatDate(endDate);

  // 获取默认日期范围（如果没有提供日期）
  const defaultRange = getDefaultDateRange();
  const finalStartDate = formattedStartDate || defaultRange.startDate;
  const finalEndDate = formattedEndDate || defaultRange.endDate;
  // 根据统计周期调整日期范围（如果提供了调整函数）
  const adjustedDates = adjustDateRangeByPeriod
    ? adjustDateRangeByPeriod(finalStartDate, finalEndDate, statPeriod)
    : { startDate: finalStartDate, endDate: finalEndDate };

  // 构建基础查询参数
  const queryParams: Record<string, any> = {
    startDate: adjustedDates.startDate,
    endDate: adjustedDates.endDate,
    statisticalDimension: STATISTICAL_DIMENSION_MAP[statPeriod] || StatisticalDimensionTypeEnum.DAY,
    regionCenterCode: userInfo?.regionCenterCode,
  };

  // 添加地区相关参数
  addRegionParams(queryParams, regionParams);
  console.log('🔥 转换后的查询参数:', queryParams);
  return queryParams as commonQueryParams;
};
