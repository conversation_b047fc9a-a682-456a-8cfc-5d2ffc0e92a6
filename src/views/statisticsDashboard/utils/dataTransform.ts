/**
 * 数据转换工具
 * 将API返回的数据转换为图表组件所需的格式
 */

import { ClueSourceEnum } from '../enums';
import type {
  AllClueSourceResponseType,
  ClueUtmInfoResponse,
  ClueValidStatisticsResponse,
  ClueValidStatusResponse,
  ClueValidFollowTimeResponse,
  ClueAllFirstRespOverTimeResponse,
  ClueConversionDataResponse,
  ClueConversionDataAnalyzeByStatusResponse,
  ClueConversionDataAnalyzeByModelResponse,
  ClueOverViewResponse,
  UserAnalysisDataResp,
  UserAnalysisChartDataResponse,
} from '../api';
import type { MonthlyChannelData, ChannelConfig, ChartDataItem, StatisticsItem, StatisticsConfig } from '../types/statisticDashboard';
import { ClueValidStatusEnum, STATUS_COLOR_MAP, getFollowUpStatusNameMap } from '../config/followUpStatusChartConfig';
import type { LeadConversionStageConfig } from '../config/leadConversionChartConfig';
import { followUpTimeRangeConfig } from '../config/FirstFollowUpTimeAnalysisChartConfig';
import { UserAnalysisChannelEnum } from '../config/userAnalysisChartConfig';
import { useI18n } from '/@/hooks/web/useI18n';

const { t } = useI18n('common');

/**
 * 统计数据提取器类型定义
 * 用于从不同的API响应中提取统计数据
 */
export type StatisticsDataExtractor<T> = (
  apiData: T,
  fieldKey: string
) => {
  total: string | number;
  ringRatio?: number;
  yearRatio?: number;
  ringRatioTrend?: boolean;  // API提供的环比趋势
  yearRatioTrend?: boolean;  // API提供的同比趋势
  hasRingData: boolean;
  hasYearData: boolean;
} | null;

/**
 * 通用的统计数据转换函数
 * 提取两个策略中相同的数据转换逻辑
 */
export function transformStatisticsData<T>(
  apiData: T,
  config: StatisticsConfig,
  fieldMapping: Record<string, string>,
  dataExtractor: StatisticsDataExtractor<T>
): StatisticsItem[] {
  console.log('🔄 开始转换统计数据:', {
    configId: config.id,
    itemCount: config.items.length,
    fieldMapping,
    apiDataKeys: Object.keys(apiData as any)
  });

  const transformedItems: StatisticsItem[] = [];

  // 遍历配置中的统计项
  for (const item of config.items) {
    if (!item.id) continue; // 跳过没有ID的项

    const apiFieldKey = fieldMapping[item.id];
    if (!apiFieldKey) {
      // 如果没有字段映射，使用兜底数据
      console.warn(`⚠️ 统计项 ${item.id} 没有对应的API字段映射，使用兜底数据。可用映射:`, Object.keys(fieldMapping));
      transformedItems.push({
        ...item,
        total: 0,
        chainComparison: undefined,
        chainTrend: undefined,
        yearComparison: undefined,
        yearTrend: undefined,
      });
      continue;
    }

    console.log(`📊 处理统计项: ${item.id} -> ${apiFieldKey}`);

    // 使用数据提取器提取数据
    const extractedData = dataExtractor(apiData, apiFieldKey);

    if (extractedData) {
      const {
        total,
        ringRatio = 0,
        yearRatio = 0,
        ringRatioTrend,
        yearRatioTrend,
        hasRingData,
        hasYearData
      } = extractedData;

      // 优先使用API提供的趋势数据，如果没有则根据正负数判断
      const finalRingRatioTrend = ringRatioTrend !== undefined ? ringRatioTrend : ringRatio >= 0;
      const finalYearRatioTrend = yearRatioTrend !== undefined ? yearRatioTrend : yearRatio >= 0;

      const transformedItem: StatisticsItem = {
        ...item, // 保留原有配置
        total,
        chainComparison: hasRingData ? Math.abs(ringRatio) : undefined,
        chainTrend: hasRingData ? finalRingRatioTrend : undefined,
        yearComparison: hasYearData ? Math.abs(yearRatio) : undefined,
        yearTrend: hasYearData ? finalYearRatioTrend : undefined,
      };

      transformedItems.push(transformedItem);
    } else {
      // 如果没有对应的API数据，使用兜底数据
      console.warn(`⚠️ 未找到统计项 ${item.id} 对应的API数据，使用兜底数据`);
      transformedItems.push({
        ...item,
        total: 0,
        chainComparison: undefined,
        chainTrend: undefined,
        yearComparison: undefined,
        yearTrend: undefined,
      });
    }
  }

  return transformedItems;
}

/**
 * ClueOverViewResponse数据提取器
 */
export const clueOverviewDataExtractor: StatisticsDataExtractor<ClueOverViewResponse> = (apiData, fieldKey) => {
  const overviewData = (apiData as any)[fieldKey];
  if (!overviewData) {
    console.warn(`⚠️ 线索总览数据提取器：未找到字段 ${fieldKey}，可用字段:`, Object.keys(apiData));
    return null;
  }

  // 兜底处理：某些字段可能不存在
  const count = overviewData.count ?? 0;
  const ringRatio = overviewData.ringRatio ?? 0;
  const yearRatio = overviewData.yearRatio ?? 0;

  // 检查是否有环比和同比数据
  const hasRingData = overviewData.hasOwnProperty('ringRatio') && overviewData.ringRatio !== null;
  const hasYearData = overviewData.hasOwnProperty('yearRatio') && overviewData.yearRatio !== null;

  // 处理百分比类型的字段
  const percentFields = ['clueFollowPercent', 'clueValidPercent', 'clueWinPercent'];
  const isPercentField = percentFields.some((field) => fieldKey.includes(field));
  const total = isPercentField ? `${overviewData.percent ?? 0}%` : count;

  console.log(`📊 线索总览数据提取 [${fieldKey}]:`, {
    total,
    ringRatio,
    yearRatio,
    hasRingData,
    hasYearData,
    isPercentField
  });

  return {
    total,
    ringRatio,
    yearRatio,
    ringRatioTrend: overviewData.ringRatioTrend,  // 使用API提供的环比趋势
    yearRatioTrend: overviewData.yearRatioTrend,  // 使用API提供的同比趋势
    hasRingData,
    hasYearData,
  };
};

/**
 * ClueConversionDataResponse数据提取器
 */
export const clueConversionDataExtractor: StatisticsDataExtractor<ClueConversionDataResponse> = (apiData, fieldKey) => {
  const conversionData = (apiData as any)[fieldKey];
  if (!conversionData || typeof conversionData !== 'object' || conversionData === null) {
    return null;
  }

  let total: string | number = 0;
  const ringRatio = conversionData.ringRatio ?? 0;
  const yearRatio = conversionData.yearRatio ?? 0;

  // 根据数据类型处理不同的字段
  if ('percent' in conversionData && typeof conversionData.percent === 'number') {
    // 百分比类型数据（转化率、战败率等）
    total = `${conversionData.percent}%`;
  } else if ('count' in conversionData && typeof conversionData.count === 'number') {
    // 数量类型数据（总线索数、已成交数）
    total = conversionData.count ?? 0;
  }

  // 检查是否有环比和同比数据
  const hasRingData = conversionData.hasOwnProperty('ringRatio');
  const hasYearData = conversionData.hasOwnProperty('yearRatio');

  return {
    total,
    ringRatio,
    yearRatio,
    hasRingData,
    hasYearData,
  };
};

/**
 * 用户分析数据提取器
 */
export const userAnalysisDataExtractor: StatisticsDataExtractor<UserAnalysisDataResp> = (apiData, fieldKey) => {
  const conversionData = (apiData as any)[fieldKey];
  if (!conversionData) {
    console.warn(`⚠️ 用户分析数据提取器：未找到字段 ${fieldKey}，可用字段:`, Object.keys(apiData));
    return null;
  }

  // 修复字段名：使用 count 而不是 account（与 ClueOverViewCountDto 接口一致）
  const total = conversionData.count ?? 0;
  const ringRatio = conversionData.ringRatio ?? 0;
  const yearRatio = conversionData.yearRatio ?? 0;

  // 检查是否有环比和同比数据
  const hasRingData = conversionData.hasOwnProperty('ringRatio') && conversionData.ringRatio !== null;
  const hasYearData = conversionData.hasOwnProperty('yearRatio') && conversionData.yearRatio !== null;

  console.log(`📊 用户分析数据提取 [${fieldKey}]:`, { total, ringRatio, yearRatio, hasRingData, hasYearData });

  return {
    total,
    ringRatio,
    yearRatio,
    hasRingData,
    hasYearData,
  };
};

/**
 * 一级来源ID到内部渠道key的映射
 */
export const CLUE_SOURCE_ID_TO_KEY_MAP: Record<string, string> = {
  [ClueSourceEnum.ONE_SOURCE_ONLINE_PUB]: 'onlinePublic',
  [ClueSourceEnum.ONE_SOURCE_ONLINE_PRI]: 'onlinePrivate',
  [ClueSourceEnum.ONE_SOURCE_OFFLINE_PRI]: 'offlinePrivate',
};

/**
 * 二级来源ID到内部子渠道key的映射
 */
export const CLUE_SOURCE_SECOND_ID_TO_KEY_MAP: Record<string, string> = {
  // 线上私域二级来源
  [ClueSourceEnum.TWO_SOURCE_ONLINE_PRI_APP]: 'app',
  [ClueSourceEnum.TWO_SOURCE_ONLINE_PRI_WEB]: 'website',
  [ClueSourceEnum.TWO_SOURCE_ONLINE_PRI_CUSTOM]: 'serviceCenter',

  // 线下私域二级来源
  [ClueSourceEnum.TWO_SOURCE_OFFLINE_PRI_MARKETING]: 'marketing',
  [ClueSourceEnum.TWO_SOURCE_OFFLINE_PRI_CARSHOW]: 'carshow',

  // 线上公域二级来源
  [ClueSourceEnum.TWO_SOURCE_ONLINE_PUB_SOCIAL]: 'social',
  [ClueSourceEnum.TWO_SOURCE_ONLINE_PUB_VERTICAL]: 'vertical',
};

/**
 * 内部渠道key到一级来源ID的反向映射
 */
export const KEY_TO_CLUE_SOURCE_ID_MAP: Record<string, string> = Object.fromEntries(
  Object.entries(CLUE_SOURCE_ID_TO_KEY_MAP).map(([id, key]) => [key, id])
);

/**
 * 内部子渠道key到二级来源ID的反向映射
 */
export const KEY_TO_CLUE_SOURCE_SECOND_ID_MAP: Record<string, string> = Object.fromEntries(
  Object.entries(CLUE_SOURCE_SECOND_ID_TO_KEY_MAP).map(([id, key]) => [key, id])
);

/**
 * 根据一级来源ID获取其对应的二级来源ID列表
 */
export const getSecondSourceIdsByFirstSourceId = (firstSourceId: string): string[] => {
  const channelKey = CLUE_SOURCE_ID_TO_KEY_MAP[firstSourceId];
  if (!channelKey) return [];

  // 根据一级来源类型返回对应的二级来源
  switch (firstSourceId) {
    case ClueSourceEnum.ONE_SOURCE_ONLINE_PRI:
      return [ClueSourceEnum.TWO_SOURCE_ONLINE_PRI_APP, ClueSourceEnum.TWO_SOURCE_ONLINE_PRI_WEB, ClueSourceEnum.TWO_SOURCE_ONLINE_PRI_CUSTOM];
    case ClueSourceEnum.ONE_SOURCE_OFFLINE_PRI:
      return [ClueSourceEnum.TWO_SOURCE_OFFLINE_PRI_MARKETING, ClueSourceEnum.TWO_SOURCE_OFFLINE_PRI_CARSHOW];
    case ClueSourceEnum.ONE_SOURCE_ONLINE_PUB:
      return [ClueSourceEnum.TWO_SOURCE_ONLINE_PUB_SOCIAL, ClueSourceEnum.TWO_SOURCE_ONLINE_PUB_VERTICAL];
    default:
      return [];
  }
};

/**
 * 转换API返回的一级来源数据为图表数据格式
 */
export function transformApiDataToChartData(apiResponse: AllClueSourceResponseType): MonthlyChannelData[] {
  const result: MonthlyChannelData[] = [];

  // 遍历每个统计日期的数据
  apiResponse.clueOneSourceResponseList.forEach((dateItem) => {
    const chartDataItem: MonthlyChannelData = {
      name: dateItem.statisticsDate,
      value: 0,
      type: 'period',
      channels: {},
      subChannels: {}, // 一级数据时二级为空，下探时再填充
    };

    // 转换一级来源数据
    dateItem.clueOneSourceDtoList.forEach((sourceItem) => {
      const channelKey = CLUE_SOURCE_ID_TO_KEY_MAP[sourceItem.oneSourceId];
      if (channelKey) {
        chartDataItem.channels[channelKey] = sourceItem.oneSourceCount;
        chartDataItem.value += sourceItem.oneSourceCount;
      } else {
        console.warn(`未知的一级来源ID: ${sourceItem.oneSourceId}`);
      }
    });

    result.push(chartDataItem);
  });

  return result;
}

// ========== 线索转化数据转换 ==========

/**
 * 转换线索转化API数据为统计项格式
 * @param apiData 线索转化API响应数据
 * @param config 统计配置
 * @returns 转换后的统计项数组
 */
export function transformClueConversionDataToStatistics(apiData: ClueConversionDataResponse, config: StatisticsConfig): StatisticsItem[] {
  // API字段到统计项ID的映射
  const fieldMapping: Record<string, string> = {
    overallConversionRate: 'clueConversionTotalPercent', // 总体转化率
    averageConversionCycle: 'clueConversionAverageCycles', // 平均转化周期
    numberOfBusCables: 'clueValidTotalCount', // 总线索数（对应numberOfBusCables）
    numberOfDeals: 'clueValidDealCount', // 已成交数
    clueDefeatRate: 'clueLosePercent', // 线索战败率
  };

  return transformStatisticsData(apiData, config, fieldMapping, clueConversionDataExtractor);
}

/**
 * 转换用户分析API数据为统计项格式
 */
export function transformUserAnalysisDataToStatistics(apiData: UserAnalysisDataResp, config: StatisticsConfig): StatisticsItem[] {
  // 修复字段映射：配置项ID -> API数据字段
  const fieldMapping: Record<string, string> = {
    userCountTotal: 'userTotal',    // 用户总量 -> userTotal
    addNewUser: 'newQuantity',      // 新增用户 -> newQuantity
    cancelUser: 'userLogoff',       // 注销用户 -> userLogoff
  };
  const result = transformStatisticsData(apiData, config, fieldMapping, userAnalysisDataExtractor);
  console.log('🔄 用户分析统计数据转换结果:', result);
  return result;
}

/**
 * 转换API返回的二级来源数据为图表数据格式
 */
export function transformSecondApiDataToChartData(apiResponse: AllClueSourceResponseType, parentSourceId: string): MonthlyChannelData[] {
  const result: MonthlyChannelData[] = [];
  const parentChannelKey = CLUE_SOURCE_ID_TO_KEY_MAP[parentSourceId];

  if (!parentChannelKey) {
    console.warn(`未知的父级来源ID: ${parentSourceId}`);
    return result;
  }

  // 遍历每个统计日期的二级数据
  apiResponse.clueTwoSourceResponseList?.forEach((dateItem) => {
    const chartDataItem: MonthlyChannelData = {
      name: dateItem.statisticsDate,
      value: 0,
      type: 'period',
      channels: {},
      subChannels: {
        [parentChannelKey]: {},
      },
    };

    // 转换二级来源数据
    dateItem.clueTwoSourceDtoList.forEach((sourceItem) => {
      const subChannelKey = CLUE_SOURCE_SECOND_ID_TO_KEY_MAP[sourceItem.twoSourceId];
      if (subChannelKey) {
        chartDataItem.subChannels![parentChannelKey][subChannelKey] = sourceItem.twoSourceCount;
        chartDataItem.value += sourceItem.twoSourceCount;
      } else {
        console.warn(`未知的二级来源ID: ${sourceItem.twoSourceId}`);
      }
    });

    result.push(chartDataItem);
  });

  return result;
}

// ========== 线索跟进状态数据转换 ==========

/**
 * 转换线索跟进状态API数据为图表数据格式（支持动态渠道配置）
 * @param apiResponse 线索跟进状态API响应数据
 * @returns 转换后的图表数据
 */
export function transformClueValidStatusToChartData(apiResponse: ClueValidStatusResponse, validStatusList: number[]): MonthlyChannelData[] {
  // 过滤掉无效的状态值，获取有效的状态列表
  const validStatuses = validStatusList.filter((status) => Object.values(ClueValidStatusEnum).includes(status));

  // 动态生成渠道配置
  const channelConfig = generateDynamicFollowUpStatusChannelConfig(validStatusList);

  return apiResponse.clueValidStatusInfoList.map((item) => {
    // 初始化channels对象
    const channels: Record<string, number> = {};

    // 计算总数
    let totalValue = 0;

    // 遍历状态详情列表，按渠道配置映射数据
    item.clueUtmDtoList.forEach((statusDto) => {
      const statusValue = statusDto.validStatus;

      // 检查状态值是否在有效的状态列表中
      if (validStatuses.includes(statusValue)) {
        const statusKey = statusValue.toString();
        // 累加到对应渠道（如果已存在则累加，否则初始化）
        channels[statusKey] = (channels[statusKey] || 0) + statusDto.statusCount;
        totalValue += statusDto.statusCount;
      } else {
        console.warn(`过滤掉无效的线索跟进状态值: ${statusValue}，不在有效状态列表中`);
      }
    });

    // 确保所有渠道都有值（默认为0）
    channelConfig.forEach((config) => {
      if (!(config.key in channels)) {
        channels[config.key] = 0;
      }
    });
    return {
      name: item.statisticsDate,
      value: totalValue,
      type: 'period' as const,
      channels,
      // 添加跟进率相关的自定义数据
      customData: {
        followUpPercent: item.clueValidStatusFollowPercent || 0,
        followUpCount: item.clueValidStatusFollowCount || 0,
        totalCount: item.clueValidStatusTotal || totalValue,
      },
    };
  });
}

/**
 * UTM渠道配置定义 - utm_source维度
 * 根据实际API数据更新渠道配置
 */
export const UTM_SOURCE_CHANNEL_CONFIG: ChannelConfig[] = [
  {
    key: 'test001',
    name: 'test001',
    color: '#5470c6', // 蓝色
  },
  {
    key: 'autoesporte',
    name: 'autoesporte',
    color: '#91cc75', // 绿色
  },
  {
    key: 'uol',
    name: 'UOL',
    color: '#fac858', // 黄色
  },
  {
    key: 'google',
    name: 'Google',
    color: '#ee6666', // 红色
  },
  {
    key: 'weach',
    name: 'Weach',
    color: '#73c0de', // 青色
  },
  {
    key: 'metaAds',
    name: 'Meta Ads',
    color: '#fc8452', // 橙色
  },
  {
    key: 'unknown',
    name: t('unknown'),
    color: '#9a60b4', // 紫色
  },
];

/**
 * UTM渠道配置定义 - utm_medium维度
 */
export const UTM_MEDIUM_CHANNEL_CONFIG: ChannelConfig[] = [
  {
    key: 'cpc',
    name: 'CPC',
    color: '#5470c6', // 蓝色
  },
  {
    key: 'social',
    name: 'Social',
    color: '#91cc75', // 绿色
  },
  {
    key: 'email',
    name: 'Email',
    color: '#fac858', // 黄色
  },
  {
    key: 'organic',
    name: 'Organic',
    color: '#ee6666', // 红色
  },
  {
    key: 'referral',
    name: 'Referral',
    color: '#73c0de', // 粉色系
  },
];

/**
 * 获取UTM渠道配置（静态配置，已废弃）
 * @deprecated 请使用 generateDynamicUtmChannelConfig 进行动态渠道配置生成
 */
export function getUtmChannelConfig(dataSource: 'utmSource' | 'utmMedium'): ChannelConfig[] {
  return dataSource === 'utmSource' ? UTM_SOURCE_CHANNEL_CONFIG : UTM_MEDIUM_CHANNEL_CONFIG;
}

/**
 * 获取UTM渠道配置（支持动态配置）
 * @param utmList API返回的UTM渠道列表
 * @param dataSource 数据源类型
 * @returns 渠道配置数组
 */
export function getDynamicUtmChannelConfig(utmList: string[], dataSource: 'utmSource' | 'utmMedium'): ChannelConfig[] {
  return generateDynamicUtmChannelConfig(utmList, dataSource);
}

/**
 * 根据UTM列表动态生成渠道配置
 * @param utmList API返回的UTM渠道列表
 * @param dataSource 数据源类型
 * @returns 动态生成的渠道配置
 */
export function generateDynamicUtmChannelConfig(utmList: string[], _dataSource: 'utmSource' | 'utmMedium'): ChannelConfig[] {
  // 预定义颜色列表
  const colors = [
    '#5470c6', // 蓝色
    '#91cc75', // 绿色
    '#fac858', // 黄色
    '#ee6666', // 红色
    '#73c0de', // 青色
    '#fc8452', // 橙色
    '#9a60b4', // 紫色
    '#ea7ccc', // 粉色
    '#5fb3d3', // 浅蓝
    '#b6a2de', // 浅紫
    '#ffb980', // 浅橙
    '#d87a80', // 浅红
  ];

  // 过滤掉null、undefined和空字符串
  const validUtmList = utmList.filter((utmName) => utmName != null && utmName.trim() !== '');

  const dynamicChannels: ChannelConfig[] = validUtmList.map((utmName, index) => ({
    key: utmName,
    name: utmName,
    color: colors[index % colors.length], // 循环使用颜色
  }));

  // 添加"其他渠道"配置
  dynamicChannels.push({
    key: 'other',
    name: t('othersChannels'),
    color: '#999999', // 灰色
  });

  return dynamicChannels;
}

/**
 * 转换UTM API数据为图表数据格式（支持动态渠道配置）
 * @param apiData API返回的UTM数据
 * @param utmList API返回的UTM渠道列表
 * @param dataSource 数据源类型
 * @returns 转换后的图表数据
 */
export function transformUtmApiDataToChartData(
  apiData: ClueUtmInfoResponse[],
  utmList: string[],
  dataSource: 'utmSource' | 'utmMedium'
): MonthlyChannelData[] {
  // 过滤掉null、undefined和空字符串，获取有效的UTM列表
  const validUtmList = utmList.filter((utmName) => utmName != null && utmName.trim() !== '');

  // 动态生成渠道配置
  const channelConfig = generateDynamicUtmChannelConfig(utmList, dataSource);

  return apiData.map((item) => {
    // 初始化channels对象
    const channels: Record<string, number> = {};

    // 计算总数
    let totalValue = 0;

    // 遍历UTM详情列表，按渠道配置映射数据
    item.clueUtmDtoList.forEach((utmDto) => {
      let channelKey: string;

      // 检查utmName是否存在
      if (!utmDto.utmName || utmDto.utmName.trim() === '') {
        // 对于没有utmName的数据，归类到"其他渠道"
        channelKey = 'other';
        console.warn('UTM数据中存在空的utmName，将归类到其他渠道:', utmDto);
      } else {
        // 检查utmName是否在有效的utmList中
        if (validUtmList.includes(utmDto.utmName)) {
          channelKey = utmDto.utmName;
        } else {
          // 如果utmName不在有效的utmList中，也归类到"其他渠道"
          console.warn(`UTM数据中存在未在有效utmList中的渠道: ${utmDto.utmName}，将归类到其他渠道`);
          channelKey = 'other';
        }
      }

      // 累加到对应渠道（如果已存在则累加，否则初始化）
      channels[channelKey] = (channels[channelKey] || 0) + utmDto.utmCount;
      totalValue += utmDto.utmCount;
    });

    // 确保所有渠道都有值（默认为0）
    channelConfig.forEach((config) => {
      if (!(config.key in channels)) {
        channels[config.key] = 0;
      }
    });

    return {
      name: item.statisticsDate,
      value: totalValue,
      type: 'period' as const,
      channels,
    };
  });
}

// ========== 线索有效性数据转换 ==========

/**
 * 转换线索有效性API数据为图表数据格式
 * @param apiResponse 线索有效性API响应数据
 * @returns 转换后的图表数据
 */
export function transformClueValidStatisticsToChartData(apiResponse: ClueValidStatisticsResponse): ChartDataItem[] {
  const result: ChartDataItem[] = [];

  // 遍历线索有效性统计信息列表
  apiResponse.clueValidStatisticsInfoList.forEach((item) => {
    // 为每个日期创建图表数据项
    const chartDataItem: ChartDataItem = {
      name: item.statisticsDate,
      value: item.clueAllTotal, // 主要值使用线索总量
      extra: {
        totalClues: item.clueAllTotal, // 线索总量
        effectiveClues: item.clueValidTotal, // 有效线索总量
        effectiveRate: item.clueValidPercent, // 线索有效率
        statisticalDimension: apiResponse.statisticalDimension, // 统计维度
      },
    };

    result.push(chartDataItem);
  });
  return result;
}

/**
 * 根据有效状态列表动态生成线索跟进状态渠道配置
 * @param validStatusList API返回的有效状态列表
 * @returns 动态生成的渠道配置
 */
export function generateDynamicFollowUpStatusChannelConfig(validStatusList: number[]): ChannelConfig[] {
  // 预定义颜色列表
  const colors = Object.entries(STATUS_COLOR_MAP).map(([_, value]) => value);
  // 过滤掉无效的状态值
  const validStatuses = validStatusList.filter((status) => Object.values(ClueValidStatusEnum).includes(status));

  // 线索跟进状态名称映射
  const STATUS_NAME_MAP = getFollowUpStatusNameMap();

  const dynamicChannels: ChannelConfig[] = validStatuses.map((statusCode, index) => ({
    key: statusCode.toString(),
    name: STATUS_NAME_MAP[statusCode] || `状态${statusCode}`,
    color: colors[index % colors.length], // 循环使用颜色
  }));

  return dynamicChannels;
}

/**
 * 获取线索跟进状态渠道配置（支持动态配置）
 * @param validStatusList API返回的有效状态列表
 * @returns 渠道配置数组
 */
export function getDynamicFollowUpStatusChannelConfig(validStatusList: number[]): ChannelConfig[] {
  return generateDynamicFollowUpStatusChannelConfig(validStatusList);
}

/**
 * 转换线索跟进时间API数据为图表数据格式
 */
export function transformFirstFollowUpTimeToChartData(apiResponse: ClueValidFollowTimeResponse): ChartDataItem[] {
  const result: ChartDataItem[] = [];

  // 使用 followUpTimeRangeConfig 的 key 进行映射
  const apiDataMapping: Record<string, any> = {
    within_4h: apiResponse.fourHour,
    within_8h: apiResponse.eightHour,
    within_12h: apiResponse.twelveHour,
    within_16h: apiResponse.sixteenHour,
    within_20h: apiResponse.twentyHour,
    within_24h: apiResponse.twentyFourHour,
    over_24h: apiResponse.overTwentyFourHour,
  };

  // 转换每个时间区间的数据，直接使用配置中的 key 进行映射
  followUpTimeRangeConfig.forEach((config) => {
    const data = apiDataMapping[config.key];
    if (data) {
      result.push({
        name: config.name,
        value: data.followTimeCount,
        percentage: data.followTimePercent,
        extra: {
          timeRange: config.key,
          count: data.followTimeCount,
          percent: data.followTimePercent,
        },
      });
    }
  });

  return result;
}

/**
 * 转换首次响应超时数据为图表数据格式
 * @param apiResponse API返回的首次响应超时数据
 * @returns 转换后的图表数据数组
 */
export function transformFirstResponseTimeoutToChartData(apiResponse: ClueAllFirstRespOverTimeResponse): ChartDataItem[] {
  // 处理API响应数据，确保数据结构正确
  const dataList = apiResponse.firstRespOverTimeInfoList || [];

  return dataList.map(
    (item) =>
      ({
        name: item.statisticsDate,
        value: item.totalCount,
        percent: item.totalCount > 0 ? (item.normalCount / item.totalCount) * 100 : 0, // 正常响应百分比
        // 扩展数据，用于图表配置中的数据提取
        timeoutCount: item.timeoutCount,
        withinTimeCount: item.normalCount,
        timeoutPercent: item.timeoutPercent,
        withinTimePercent: item.normalPercent,
        totalCount: item.totalCount,
      }) as ChartDataItem & {
        timeoutCount: number;
        withinTimeCount: number;
        timeoutPercent: number;
        withinTimePercent: number;
        totalCount: number;
      }
  );
}

/**
 * 转换线索转化分析数据为图表数据
 * 将API返回的ClueConversionDataAnalyzeByStatusResponse转换为漏斗图所需的LeadConversionStageConfig格式
 */
export function transformClueConversionDataAnalyzeByStatusToChartData(
  apiResponse: ClueConversionDataAnalyzeByStatusResponse
): LeadConversionStageConfig[] {
  const statusNameMap = getFollowUpStatusNameMap();
  const chartData: LeadConversionStageConfig[] = [];
  const statusColor = '#1890ff'; // 固定该颜色

  // 首先添加总线索数据，无转换率
  chartData.push({
    name: '总线索',
    value: apiResponse.clueAllTotalCount,
    color: statusColor,
  });

  // 添加有效线索数据
  chartData.push({
    name: '有效线索',
    value: apiResponse.clueValidConversion.statusCount || 0,
    conversionRate: apiResponse.clueValidConversion.conversionPercent || 0,
    color: statusColor,
  });

  // 按状态枚举值排序，确保漏斗图的正确顺序
  // const sortedStatusData = apiResponse.clueValidStatusConversion.sort((a, b) => a.validStatus - b.validStatus);
  apiResponse.clueValidStatusConversion?.forEach((statusItem) => {
    const statusName = statusNameMap[statusItem.validStatus] || `状态${statusItem.validStatus}`;
    chartData.push({
      name: statusName,
      value: statusItem.statusCount,
      conversionRate: statusItem.conversionPercent,
      color: statusColor,
    });
  });

  return chartData;
}

/**
 * 转换各车型成交周期分析数据为图表数据
 * 将API返回的ClueConversionDataAnalyzeByModelResponse转换为图表所需的ChartDataItem格式
 */
export function transformClueConversionDataAnalyzeByModelToChartData(apiResponse: ClueConversionDataAnalyzeByModelResponse): ChartDataItem[] {
  const chartData: ChartDataItem[] = [];

  // 处理各车型成交周期数据
  if (apiResponse.clueValidModelConversion && Array.isArray(apiResponse.clueValidModelConversion)) {
    apiResponse.clueValidModelConversion.forEach((modelItem) => {
      chartData.push({
        name: modelItem.modelCode, // 车型代码作为名称
        value: modelItem.conversionDay, // 成交周期天数作为值
        extra: {
          modelCode: modelItem.modelCode, // 车型代码
          conversionDay: modelItem.conversionDay, // 成交周期
          averageCycles: apiResponse.clueConversionAverageCycles, // 平均转化周期
          statisticalDimension: apiResponse.statisticalDimension, // 统计维度
        },
      });
    });
  }

  return chartData;
}

/**
 * 转换用户分析图表数据
 * 将UserAnalysisChartDataResponse转换为ChartDataItem[]格式
 * @param apiResponse 用户分析API响应数据
 * @returns 转换后的图表数据
 */
/**
 * 用户分析渠道数据映射配置
 * 定义API字段到渠道枚举的映射关系
 */
const USER_ANALYSIS_CHANNEL_FIELD_MAP = {
  [UserAnalysisChannelEnum.APP]: 'appTotal',
  [UserAnalysisChannelEnum.CCS]: 'ccsTotal',
  [UserAnalysisChannelEnum.OFFICIAL]: 'officialTotal',
  [UserAnalysisChannelEnum.OTHER]: 'otherTotal',
} as const;

/**
 * 用户分析数据类型枚举
 */
export enum UserAnalysisDataTypeEnum {
  /** 用户注册 */
  REGISTER = 'register',
  /** 用户注销 */
  LOGOFF = 'logoff',
}

/**
 * 创建用户分析图表数据项
 * @param date 统计日期
 * @param data API返回的渠道数据
 * @param dataType 数据类型（注册/注销）
 * @returns 图表数据项
 */
function createUserAnalysisChartDataItem(date: string, data: any, dataType: UserAnalysisDataTypeEnum): ChartDataItem {
  const customData: Record<string, any> = {
    date: date,
    type: dataType,
    total: data.total || 0,
  };

  // 使用枚举值动态构建渠道数据
  Object.values(UserAnalysisChannelEnum).forEach((channelKey) => {
    const apiFieldKey = USER_ANALYSIS_CHANNEL_FIELD_MAP[channelKey];
    if (apiFieldKey) {
      customData[`${channelKey}Channel`] = data[apiFieldKey] || 0;
    }
  });

  return {
    name: date,
    value: data.total || 0,
    category: dataType,
    customData,
  };
}

/**
 * 用户分析数据结构化结果
 */
export interface UserAnalysisStructuredData {
  dates: string[];
  registerMap: Map<string, ChartDataItem>;
  logoffMap: Map<string, ChartDataItem>;
  chartData: ChartDataItem[];
}

/**
 * 转换用户分析API数据为图表数据格式
 * 一次性构建完整的结构化数据，避免重复映射
 * @param apiResponse 用户分析API响应数据
 * @returns 转换后的图表数据数组
 */
export function transformUserAnalysisChartDataToChartData(apiResponse: UserAnalysisChartDataResponse): ChartDataItem[] {
  if (!apiResponse) {
    console.warn('⚠️ 用户分析API数据为空:', apiResponse);
    return [];
  }

  const { registerList = [], logoffList = [] } = apiResponse;
  const chartData: ChartDataItem[] = [];

  // 提取所有日期
  const allDates = new Set<string>();
  registerList.forEach((item) => allDates.add(item.statisticsDate));
  logoffList.forEach((item) => allDates.add(item.statisticsDate));
  const sortedDates = Array.from(allDates).sort();

  // 一次性构建完整的结构化数据
  const registerMap = new Map<string, ChartDataItem>();
  const logoffMap = new Map<string, ChartDataItem>();

  // 创建注册数据项并构建映射
  registerList.forEach((item) => {
    const chartDataItem = createUserAnalysisChartDataItem(item.statisticsDate, item, UserAnalysisDataTypeEnum.REGISTER);
    registerMap.set(item.statisticsDate, chartDataItem);
    chartData.push(chartDataItem);
  });

  // 创建注销数据项并构建映射
  logoffList.forEach((item) => {
    const chartDataItem = createUserAnalysisChartDataItem(item.statisticsDate, item, UserAnalysisDataTypeEnum.LOGOFF);
    logoffMap.set(item.statisticsDate, chartDataItem);
    chartData.push(chartDataItem);
  });

  // 将完整的结构化数据嵌入到每个数据项中（只需要嵌入一次）
  const structuredMetadata = {
    dates: sortedDates,
    registerMap,
    logoffMap,
  };

  // 将结构化元数据嵌入到第一个数据项中即可
  if (chartData.length > 0) {
    chartData[0].customData = {
      ...chartData[0].customData,
      _structuredMetadata: structuredMetadata,
    };
  }

  console.log('🎯 用户分析数据转换完成:', {
    原始注册数据: registerList.length,
    原始注销数据: logoffList.length,
    转换后数据: chartData.length,
    日期范围: sortedDates.length > 0 ? `${sortedDates[0]} ~ ${sortedDates[sortedDates.length - 1]}` : '无数据',
    支持的渠道: Object.values(UserAnalysisChannelEnum),
  });

  return chartData;
}
