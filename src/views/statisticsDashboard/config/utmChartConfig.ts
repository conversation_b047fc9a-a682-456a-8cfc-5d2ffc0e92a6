import { useI18n } from '/@/hooks/web/useI18n';
import type { ChannelConfig, ChartConfig, ChartDataItem, MonthlyChannelData } from '../types/statisticDashboard';
import { createTooltipFormatter, generateChartDataItem } from '../utils';
import { getDynamicUtmChannelConfig } from '../utils/dataTransform';

const { t } = useI18n('common');

const formatUtmTooltip = createTooltipFormatter({
  showTotal: true,
  excludeSeriesTypes: ['line'],
  specialSeries: { type: 'line', label: t('totalTrend') },
  totalLabel: t('total'),
});

export class UtmChartConfigManager {
  private static instance: UtmChartConfigManager;
  private loadingStrategy = 'utm-chart';
  private constructor() {}

  public static getInstance(): UtmChartConfigManager {
    if (!UtmChartConfigManager.instance) {
      UtmChartConfigManager.instance = new UtmChartConfigManager();
    }
    return UtmChartConfigManager.instance;
  }

  public generateBaseConfig(dataSource: 'utmSource' | 'utmMedium' = 'utmSource', utmList?: string[]): ChartConfig {
    const isUtmSource = dataSource === 'utmSource';
    const channelConfig = this.getChannelConfig(dataSource, utmList);
    const title = UtmChartConfigManager.getTitleByDataSource(dataSource);

    const stackSeries = channelConfig.map((channelItem) => ({
      name: channelItem.name,
      type: 'bar',
      stack: 'utm',
      data: [],
      itemStyle: { color: channelItem.color },
      label: { show: false },
    }));

    const trendSeries = {
      name: t('totalTrend'),
      type: 'line',
      data: [],
      itemStyle: { color: '#8B0000' },
      lineStyle: { color: '#8B0000', width: 2 },
      symbol: 'circle',
      symbolSize: 6,
      yAxisIndex: 0,
    };

    const series = [...stackSeries, trendSeries];

    return {
      id: 'utmChart',
      type: 'bar',
      title,
      dataSource,
      loadingStrategy: this.loadingStrategy,
      customProps: {
        needsAsyncData: true,
        switchable: true,
        currentDataSource: dataSource,
        alternativeDataSource: isUtmSource ? 'utmMedium' : 'utmSource',
        alternativeTitle: isUtmSource ? 'utm_medium' : 'utm_source',
        supportedDataSources: ['utmSource', 'utmMedium'],
      },
      options: {
        title: { show: false },
        color: [...channelConfig.map((config) => config.color), '#8B0000'],
        grid: { left: '4%', right: '4%', bottom: '15%', containLabel: true },
        legend: { data: [...channelConfig.map((config) => config.name), t('totalTrend')], bottom: '5%', left: 'center', type: 'scroll' },
        xAxis: { type: 'category', data: [], axisLabel: { interval: 0, rotate: 30 } },
        yAxis: { type: 'value', name: t('numberOfClues') },
        series: series as any,
        tooltip: { show: true, trigger: 'axis', axisPointer: { type: 'shadow' }, formatter: formatUtmTooltip },
      },
      size: { height: 450 },
      position: { x: 0, y: 0 },
    };
  }

  public updateChartData(baseConfig: ChartConfig, chartData: ChartDataItem[], utmList: string[]): Partial<ChartConfig> {
    const dataSource = baseConfig.dataSource as 'utmSource' | 'utmMedium';
    const channelConfig = this.getChannelConfig(dataSource, utmList);
    const months = chartData.map((item) => item.name);

    const stackSeries = channelConfig.map((channelItem) => ({
      name: channelItem.name,
      type: 'bar',
      stack: 'utm',
      data: chartData.map((monthData) => {
        const value = monthData.channels[channelItem.key] || 0;
        return generateChartDataItem(monthData.month, value, '0');
      }),
      itemStyle: { color: channelItem.color },
      label: { show: false },
    }));

    const trendData = chartData.map((monthData) => {
      const value = channelConfig.reduce((sum, config) => sum + (monthData.channels[config.key] || 0), 0);
      return generateChartDataItem(monthData.month, value, '0');
    });

    const trendSeries = {
      name: t('totalTrend'),
      type: 'line',
      data: trendData,
      itemStyle: { color: '#8B0000' },
      lineStyle: { color: '#8B0000', width: 2 },
      symbol: 'circle',
      symbolSize: 6,
      yAxisIndex: 0,
    };

    return {
      options: {
        ...baseConfig.options,
        xAxis: { ...baseConfig.options?.xAxis, type: 'category', data: months },
        series: [...stackSeries, trendSeries] as any,
        legend: { ...baseConfig.options?.legend, data: [...channelConfig.map((config) => config.name), t('totalTrend')] },
        tooltip: { ...baseConfig.options?.tooltip },
      },
      customProps: {
        ...baseConfig.customProps,
        loading: false,
        isEmpty: chartData.length === 0,
        lastUpdated: new Date().toISOString(),
      },
    };
  }

  private getChannelConfig(dataSource: 'utmSource' | 'utmMedium', utmList?: string[]): ChannelConfig[] {
    return utmList && utmList.length > 0 ? getDynamicUtmChannelConfig(utmList, dataSource) : [];
  }

  public static getAlternativeInfo(currentDataSource: string): { alternative: string; alternativeTitle: string } {
    const isUtmSource = currentDataSource === 'utmSource';
    return {
      alternative: isUtmSource ? 'utmMedium' : 'utmSource',
      alternativeTitle: isUtmSource ? 'utm_medium' : 'utm_source',
    };
  }

  public static getTitleByDataSource(dataSource: string): string {
    return dataSource === 'utmMedium' ? 'utm_medium' : 'utm_source';
  }
}

export const utmChartConfigManager = UtmChartConfigManager.getInstance();
export const generateBaseUtmChartConfig = (dataSource: 'utmSource' | 'utmMedium' = 'utmSource', utmList?: string[]): ChartConfig =>
  utmChartConfigManager.generateBaseConfig(dataSource, utmList);
export const updateUtmChartData = (baseConfig: ChartConfig, chartData: MonthlyChannelData[], utmList: string[]): Partial<ChartConfig> =>
  utmChartConfigManager.updateChartData(baseConfig, chartData, utmList);
export const getUtmChartConfigManager = (): UtmChartConfigManager => utmChartConfigManager;
export const utmChartConfig: ChartConfig = generateBaseUtmChartConfig('utmSource');
