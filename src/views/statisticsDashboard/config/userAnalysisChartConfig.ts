/**
 * 用户分析图表配置管理器
 * 负责生成和更新用户分析图表的配置
 * 包含两条趋势线：用户新增、用户注销
 */

import type { ChartConfig, ChartDataItem } from '../types/statisticDashboard';
import { useI18n } from '/@/hooks/web/useI18n';

const { t } = useI18n('common');

/**
 * 用户分析渠道类型枚举
 */
export enum UserAnalysisChannelEnum {
  /** APP渠道 */
  APP = 'app',
  /** CCS渠道 */
  CCS = 'ccs',
  /** 官网渠道 */
  OFFICIAL = 'official',
  /** 其他渠道 */
  OTHER = 'other',
}

/**
 * 用户分析渠道颜色映射
 */
export const USER_ANALYSIS_COLOR_MAP: Record<string, string> = {
  [UserAnalysisChannelEnum.APP]: '#91cc75', // 绿色
  [UserAnalysisChannelEnum.CCS]: '#5470c6', // 蓝色
  [UserAnalysisChannelEnum.OFFICIAL]: '#fac858', // 黄色
  [UserAnalysisChannelEnum.OTHER]: '#ee6666', // 红色
  register: '#73c0de', // 青色 - 用户新增趋势线
  logoff: '#fc8452', // 橙色 - 用户注销趋势线
};

/**
 * 获取用户分析渠道名称映射
 */
export function getUserAnalysisChannelNameMap(): Record<string, string> {
  return {
    [UserAnalysisChannelEnum.APP]: t('appChannel'),
    [UserAnalysisChannelEnum.CCS]: t('ccsChannel'),
    [UserAnalysisChannelEnum.OFFICIAL]: t('officialChannel'),
    [UserAnalysisChannelEnum.OTHER]: t('othersChannels'),
    register: t('userAddition'),
    logoff: t('userLogout'),
  };
}

/**
 * 获取用户分析渠道颜色映射
 */
export function getUserAnalysisChannelColorMap(): Record<string, string> {
  return USER_ANALYSIS_COLOR_MAP;
}

/**
 * 用户分析图表tooltip格式化器
 * 由于需要特殊的分组展示逻辑，使用自定义实现
 */

/**
 * 渲染渠道分布的HTML
 */
function renderChannelDistribution(channels: Array<{ name: string; value: number; color: string }>, total: number): string {
  return channels
    .filter((channel) => channel.value > 0)
    .sort((a, b) => b.value - a.value)
    .map((channel) => {
      const percentage = total > 0 ? ((channel.value / total) * 100).toFixed(1) : '0.0';
      return `
        <div style="display: flex; align-items: center; margin-left: 20px; margin-bottom: 3px; font-size: 11px;">
          <span style="display: inline-block; width: 8px; height: 8px; background-color: ${channel.color}; margin-right: 6px; border-radius: 1px;"></span>
          <span style="flex: 1; color: #666;">${channel.name}：</span>
          <span style="color: #333; margin-right: 6px;">${channel.value}</span>
          <span style="color: #999; font-size: 10px;">(${percentage}%)</span>
        </div>
      `;
    })
    .join('');
}

/**
 * 从customData获取渠道分布数据
 */
function getChannelDataFromCustomData(customData: any): Array<{ name: string; value: number; color: string }> {
  const nameMap = getUserAnalysisChannelNameMap();
  const colorMap = getUserAnalysisChannelColorMap();

  return [
    { name: nameMap[UserAnalysisChannelEnum.APP], value: customData.appChannel || 0, color: colorMap[UserAnalysisChannelEnum.APP] },
    { name: nameMap[UserAnalysisChannelEnum.CCS], value: customData.ccsChannel || 0, color: colorMap[UserAnalysisChannelEnum.CCS] },
    { name: nameMap[UserAnalysisChannelEnum.OFFICIAL], value: customData.officialChannel || 0, color: colorMap[UserAnalysisChannelEnum.OFFICIAL] },
    { name: nameMap[UserAnalysisChannelEnum.OTHER], value: customData.otherChannel || 0, color: colorMap[UserAnalysisChannelEnum.OTHER] },
  ];
}

/**
 * 优化后的用户分析图表tooltip格式化函数
 * 直接使用结构化数据，统一渠道分布展示逻辑
 */
function formatUserAnalysisTooltip(params: any): string {
  if (!Array.isArray(params)) {
    params = [params];
  }

  const date = params[0]?.axisValue || '';
  const nameMap = getUserAnalysisChannelNameMap();

  let tooltipContent = `<div style="font-weight: bold; margin-bottom: 10px; font-size: 14px; color: #333;">${date}</div>`;

  // 获取趋势线数据
  const trendItems = params.filter((item: any) => item.seriesType === 'line');

  // 按用户新增/注销分组展示
  trendItems.forEach((trendItem: any) => {
    const trendName = nameMap[trendItem.seriesName] || trendItem.seriesName;
    const trendValue = trendItem.value || 0;
    const trendColor = trendItem.color || '#ccc';

    tooltipContent += `
      <div style="margin-bottom: 12px; border-top: 1px solid #eee; padding-top: 8px;">
        <div style="display: flex; align-items: center; margin-bottom: 6px; font-size: 13px; font-weight: bold;">
          <span style="display: inline-block; width: 12px; height: 3px; background-color: ${trendColor}; margin-right: 8px; border-radius: 1px;"></span>
          <span style="color: #333;">${trendName}：${trendValue}</span>
        </div>
    `;

    // 统一从customData获取渠道分布
    const customData = trendItem.data?.customData;
    if (customData && trendValue > 0) {
      const channels = getChannelDataFromCustomData(customData);
      const channelTotal = channels.reduce((sum, channel) => sum + channel.value, 0);

      if (channelTotal > 0) {
        tooltipContent += renderChannelDistribution(channels, channelTotal);
      }
    }

    tooltipContent += '</div>';
  });

  return tooltipContent;
}

/**
 * 用户分析图表配置管理器
 * 单例模式，负责生成和更新用户分析图表配置
 */
export class UserAnalysisChartConfigManager {
  private static instance: UserAnalysisChartConfigManager;

  private constructor() {}

  public static getInstance(): UserAnalysisChartConfigManager {
    if (!UserAnalysisChartConfigManager.instance) {
      UserAnalysisChartConfigManager.instance = new UserAnalysisChartConfigManager();
    }
    return UserAnalysisChartConfigManager.instance;
  }

  /**
   * 创建堆叠柱状图系列配置
   * @param channelKey 渠道键名
   * @param channelName 渠道显示名称
   * @param color 渠道颜色
   * @returns 系列配置
   */
  private createStackSeries(channelKey: string, channelName: string, color: string) {
    return {
      name: channelName, // 使用中文名称作为系列名
      channelKey: channelKey, // 保存原始key用于数据映射
      type: 'bar',
      stack: 'userAnalysis',
      data: [],
      itemStyle: {
        color: color,
      },
      emphasis: {
        focus: 'series',
      },
      label: {
        show: true,
        position: 'inside',
        formatter: (params: any) => {
          // 只显示非零值，避免标签重叠
          return params.value > 0 ? params.value : '';
        },
        fontSize: 11,
        fontWeight: 'bold',
        color: '#fff',
      },
    };
  }

  /**
   * 创建趋势线系列配置
   * @param trendKey 趋势线键名 ('register' | 'logoff')
   * @param trendName 趋势线显示名称
   * @param color 趋势线颜色
   * @returns 趋势线系列配置
   */
  private createTrendLineSeries(trendKey: string, trendName: string, color: string) {
    return {
      name: trendName, // 使用中文名称作为系列名
      trendKey: trendKey, // 保存原始key用于数据映射
      type: 'line',
      data: [],
      itemStyle: {
        color: color,
      },
      lineStyle: {
        color: color,
        width: 3,
      },
      symbol: 'circle',
      symbolSize: 8,
      label: {
        show: true,
        position: 'top',
        formatter: (params: any) => {
          return params.value > 0 ? params.value : '';
        },
        color: color,
      },
      emphasis: {
        focus: 'series',
        symbolSize: 10,
      },
    };
  }

  /**
   * 创建双Y轴配置
   * @returns 双Y轴配置数组
   */
  private createDualYAxisConfig() {
    return [
      {
        type: 'value',
        name: t('numberOfUsers'),
        position: 'left',
        splitLine: {
          show: true,
          lineStyle: {
            color: '#f5f5f5',
            type: 'dashed',
          },
        },
      },
    ];
  }

  /**
   * 生成用户分析图表的基础配置
   * @param title 图表标题
   * @returns 图表基础配置
   */
  public generateBaseConfig(): ChartConfig {
    const nameMap = getUserAnalysisChannelNameMap();
    const colorMap = getUserAnalysisChannelColorMap();

    // 创建堆叠柱状图系列（渠道分布）
    const stackedSeries = [
      this.createStackSeries(UserAnalysisChannelEnum.APP, nameMap[UserAnalysisChannelEnum.APP], colorMap[UserAnalysisChannelEnum.APP]),
      this.createStackSeries(UserAnalysisChannelEnum.CCS, nameMap[UserAnalysisChannelEnum.CCS], colorMap[UserAnalysisChannelEnum.CCS]),
      this.createStackSeries(UserAnalysisChannelEnum.OFFICIAL, nameMap[UserAnalysisChannelEnum.OFFICIAL], colorMap[UserAnalysisChannelEnum.OFFICIAL]),
      this.createStackSeries(UserAnalysisChannelEnum.OTHER, nameMap[UserAnalysisChannelEnum.OTHER], colorMap[UserAnalysisChannelEnum.OTHER]),
    ];

    // 创建趋势线系列
    const trendSeries = [
      this.createTrendLineSeries('register', nameMap.register, colorMap.register),
      this.createTrendLineSeries('logoff', nameMap.logoff, colorMap.logoff),
    ];

    return {
      id: 'user-analysis-chart',
      type: 'bar',
      title: t('userAnalysis'),
      dataSource: 'user-analysis-chart',
      loadingStrategy: 'user-analysis-chart',
      customProps: {
        needsAsyncData: true,
      },
      options: {
        title: {
          show: false,
        },
        tooltip: {
          trigger: 'axis',
          formatter: formatUserAnalysisTooltip,
        },
        legend: {
          data: [
            ...stackedSeries.map((s) => s.name), // 只需要名称，颜色由系列配置提供
            ...trendSeries.map((s) => s.name), // 只需要名称，颜色由系列配置提供
          ],
          bottom: '5%',
          type: 'scroll',
        },
        grid: {
          left: '3%',
          right: '8%',
          bottom: '15%',
          top: '10%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: [],
        },
        yAxis: this.createDualYAxisConfig() as any,
        series: [...stackedSeries, ...trendSeries] as any,
      },
      layout: {
        columnSpan: 2,
        maxWidth: '50%',
      },
    };
  }
}

// 导出单例实例
export const userAnalysisChartConfigManager = UserAnalysisChartConfigManager.getInstance();

// 导出便捷方法
export const generateUserAnalysisChartConfig = () => {
  return userAnalysisChartConfigManager.generateBaseConfig();
};

/**
 * 更新用户分析图表数据
 * @param baseConfig 基础图表配置
 * @param chartData 图表数据数组（包含嵌入的结构化数据）
 * @returns 更新后的图表配置
 */
export const updateUserAnalysisChartData = (baseConfig: ChartConfig, chartData: ChartDataItem[]): Partial<ChartConfig> => {
  // 从第一个数据项中直接获取结构化元数据,如果没有数据，直接返回 baseConfig
  const firstItem = chartData[0];
  if (!firstItem?.customData?._structuredMetadata) {
    return baseConfig;
  }

  const { dates: uniqueDates, registerMap, logoffMap } = firstItem.customData._structuredMetadata;

  // 更新系列数据
  const updatedSeries =
    (baseConfig.options?.series as any[])?.map((series: any) => {
      const channelKey = series.channelKey; // 从系列配置中获取原始channelKey
      const trendKey = series.trendKey; // 从系列配置中获取原始trendKey

      // 处理渠道堆叠柱状图系列（通过channelKey判断）
      if (channelKey && Object.values(UserAnalysisChannelEnum).includes(channelKey)) {
        const channelFieldKey = `${channelKey}Channel`;

        return {
          ...series,
          data: uniqueDates.map((date: string) => {
            const registerItem = registerMap.get(date);
            const registerValue = registerItem?.customData?.[channelFieldKey] || 0;

            return {
              name: date,
              value: registerValue,
              customData: {
                date,
                channel: channelKey,
                type: 'register',
                value: registerValue,
              },
            };
          }),
        };
      }

      // 处理趋势线系列（通过trendKey判断）
      if (trendKey && (trendKey === 'register' || trendKey === 'logoff')) {
        const dataMap = trendKey === 'register' ? registerMap : logoffMap;

        return {
          ...series,
          data: uniqueDates.map((date: string) => {
            const dataItem = dataMap.get(date);
            const value = dataItem?.customData?.total || 0;

            // 构建包含渠道分布的customData
            const customData: Record<string, any> = {
              date,
              type: trendKey,
              total: value,
            };

            // 添加渠道分布数据
            if (dataItem?.customData) {
              Object.values(UserAnalysisChannelEnum).forEach((channelKey) => {
                const channelFieldKey = `${channelKey}Channel`;
                customData[channelFieldKey] = dataItem.customData[channelFieldKey] || 0;
              });
            }

            return {
              name: date,
              value: value,
              customData,
            };
          }),
        };
      }

      return series;
    }) || [];
  return {
    options: {
      ...baseConfig.options,
      xAxis: {
        ...baseConfig.options?.xAxis,
        data: uniqueDates,
      },
      series: updatedSeries,
    },
    customProps: {
      ...baseConfig.customProps,
      isEmpty: chartData.length === 0,
      lastUpdated: new Date().toISOString(),
    },
  };
};

export const getUserAnalysisChartConfigManager = (): UserAnalysisChartConfigManager => userAnalysisChartConfigManager;
export const userAnalysisChartConfig: ChartConfig = generateUserAnalysisChartConfig();
