import { useI18n } from '/@/hooks/web/useI18n';
import type { ChartConfig, ChartDataItem } from '../types/statisticDashboard';
import { generateChartDataItem, createTooltipFormatter } from '../utils';

const { t } = useI18n('common');

const formatEffectivenessTooltip = createTooltipFormatter({
  extraInfoProvider: (_axisValue: string, params: any[]) => {
    const totalCluesParam = params.find((param) => param.seriesName === t('totalNumberOfLeads'));
    if (totalCluesParam?.data?.extra?.effectiveRate) {
      const effectiveRate = totalCluesParam.data.extra.effectiveRate.toFixed(1);
      return `<div style="margin-left: 15px; color: #666; font-size: 12px;">${t('efficient')}: ${effectiveRate}%</div>`;
    }
    return '';
  },
});

export class ClueEffectivenessChartConfigManager {
  private static instance: ClueEffectivenessChartConfigManager;
  private loadingStrategy = 'clue-effectiveness';

  private constructor() {}

  public static getInstance(): ClueEffectivenessChartConfigManager {
    if (!ClueEffectivenessChartConfigManager.instance) {
      ClueEffectivenessChartConfigManager.instance = new ClueEffectivenessChartConfigManager();
    }
    return ClueEffectivenessChartConfigManager.instance;
  }

  public generateBaseConfig(): ChartConfig {
    const title = t('clueValidity');

    const totalCluesSeries = {
      name: t('totalNumberOfLeads'),
      type: 'line',
      data: [],
      smooth: true,
      itemStyle: { color: '#5470c6' },
      lineStyle: { color: '#5470c6', width: 2 },
      symbol: 'circle',
      symbolSize: 6,
    };

    const effectiveCluesSeries = {
      name: t('totalNumberOfEffectiveClues'),
      type: 'line',
      data: [],
      smooth: true,
      itemStyle: { color: '#91cc75' },
      lineStyle: { color: '#91cc75', width: 2 },
      symbol: 'circle',
      symbolSize: 6,
    };

    const series = [totalCluesSeries, effectiveCluesSeries];

    return {
      id: 'clueEffectiveness',
      type: 'line',
      title,
      dataSource: 'clueEffectiveness',
      loadingStrategy: this.loadingStrategy,
      customProps: {
        needsAsyncData: true,
        switchable: false,
        currentDataSource: 'clueEffectiveness',
        supportedDataSources: ['clueEffectiveness'],
      },
      options: {
        title: { show: false },
        color: ['#5470c6', '#91cc75'],
        grid: { left: '4%', right: '4%', bottom: '15%', containLabel: true },
        legend: { data: [t('totalNumberOfLeads'), t('totalNumberOfEffectiveClues')], bottom: '5%', left: 'center' },
        xAxis: { type: 'category', data: [], axisLabel: { interval: 0, rotate: 30 } },
        yAxis: { type: 'value', name: t('numberOfClues'), min: 0 },
        series: series as any,
        tooltip: { show: true, trigger: 'axis', axisPointer: { type: 'cross' }, formatter: formatEffectivenessTooltip },
      },
      size: { height: 450 },
      position: { x: 0, y: 0 },
    };
  }

  public updateChartData(baseConfig: ChartConfig, chartData: ChartDataItem[]): Partial<ChartConfig> {
    const dates = chartData.map((item) => item.name);

    const totalCluesData = chartData.map((item) => {
      const value = item.extra?.totalClues || item.value;
      const dataItem = generateChartDataItem(item.name, value, '0');
      if (item.extra) (dataItem as any).extra = item.extra;
      return dataItem;
    });

    const effectiveCluesData = chartData.map((item) => {
      const value = item.extra?.effectiveClues || 0;
      const dataItem = generateChartDataItem(item.name, value, '0');
      if (item.extra) (dataItem as any).extra = item.extra;
      return dataItem;
    });

    const totalCluesSeries = {
      name: t('totalNumberOfLeads'),
      type: 'line',
      data: totalCluesData,
      smooth: true,
      itemStyle: { color: '#5470c6' },
      lineStyle: { color: '#5470c6', width: 2 },
      symbol: 'circle',
      symbolSize: 6,
    };

    const effectiveCluesSeries = {
      name: t('totalNumberOfEffectiveClues'),
      type: 'line',
      data: effectiveCluesData,
      smooth: true,
      itemStyle: { color: '#91cc75' },
      lineStyle: { color: '#91cc75', width: 2 },
      symbol: 'circle',
      symbolSize: 6,
    };

    return {
      options: {
        ...baseConfig.options,
        xAxis: { ...baseConfig.options?.xAxis, type: 'category', data: dates },
        series: [totalCluesSeries, effectiveCluesSeries] as any,
        legend: { ...baseConfig.options?.legend, data: [t('totalNumberOfLeads'), t('totalNumberOfEffectiveClues')] },
        tooltip: { ...baseConfig.options?.tooltip },
      },
      customProps: {
        ...baseConfig.customProps,
        loading: false,
        isEmpty: chartData.length === 0,
        lastUpdated: new Date().toISOString(),
      },
    };
  }
}

export const clueEffectivenessChartConfigManager = ClueEffectivenessChartConfigManager.getInstance();

export const generateBaseClueEffectivenessChartConfig = (): ChartConfig => clueEffectivenessChartConfigManager.generateBaseConfig();

export const updateClueEffectivenessChartData = (baseConfig: ChartConfig, chartData: ChartDataItem[]): Partial<ChartConfig> =>
  clueEffectivenessChartConfigManager.updateChartData(baseConfig, chartData);

export const getClueEffectivenessChartConfigManager = (): ClueEffectivenessChartConfigManager => clueEffectivenessChartConfigManager;

export const clueEffectivenessChartConfig: ChartConfig = generateBaseClueEffectivenessChartConfig();
