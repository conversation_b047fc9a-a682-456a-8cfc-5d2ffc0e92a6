import { useI18n } from '/@/hooks/web/useI18n';
import type { ChartConfig, ChartDataItem } from '../types/statisticDashboard';
import { createTooltipFormatter } from '../utils';
import { useI18nWithModule } from '/@/hooks/web/useI18nEnhanced';

const { t } = useI18n('common');

/**
 * 车型成交周期配置接口
 */
export interface VehicleModelDealCycleConfig {
  /** 车型名称 */
  name: string;
  /** 成交周期（天） */
  dealCycle: number;
  /** 车型颜色 */
  color?: string;
}

/**
 * 各车型成交分析数据接口
 */
export interface VehicleModelDealAnalysisData {
  /** 车型数据 */
  models: VehicleModelDealCycleConfig[];
  /** 平均成交周期 */
  averageDealCycle: number;
}

const formatVehicleModelDealTooltip = createTooltipFormatter({
  showPercentage: false,
  excludeSeriesTypes: ['line'],
  specialSeries: { type: 'line', label: t('averageTransactionCycle') },
  extraInfoProvider: (_axisValue: string) => {
    return '';
  },
  valueFormatter: (value: number) => `${value}${t('time_days')}`,
});

export class VehicleModelDealChartConfigManager {
  private static instance: VehicleModelDealChartConfigManager;
  private loadingStrategy = 'vehicle-model-deal-analysis';

  private constructor() {}

  public static getInstance(): VehicleModelDealChartConfigManager {
    if (!VehicleModelDealChartConfigManager.instance) {
      VehicleModelDealChartConfigManager.instance = new VehicleModelDealChartConfigManager();
    }
    return VehicleModelDealChartConfigManager.instance;
  }

  public generateBaseConfig(): ChartConfig {
    const title = t('vehicleModelDealAnalysis');
    const i18nClue = useI18nWithModule('clue-vue');
    return {
      id: 'vehicleModelDealAnalysis',
      type: 'bar',
      title,
      dataSource: 'vehicleModelDealAnalysis',
      loadingStrategy: this.loadingStrategy,
      customProps: {
        needsAsyncData: true,
      },
      options: {
        title: { show: false },
        color: ['#5470c6', '#1890ff'],
        grid: {
          left: '12%',
          right: '12%',
          bottom: '5%',
          top: '20%',
          containLabel: true,
        },
        legend: {
          data: [t('transactionCycle'), t('averageTransactionCycle')],
          bottom: '5%',
          left: 'center',
          show: false,
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLabel: {
            interval: 0,
            rotate: 30,
            fontSize: 12,
          },
          name: i18nClue.t('clue.order.model.name'),
          nameLocation: 'end',
          nameTextStyle: {
            fontSize: 12,
            color: '#666',
            padding: [0, 0, 0, 0],
          },
          axisLine: {
            show: true,
          },
        },
        yAxis: {
          type: 'value',
          name: t('transactionCycle'),
          nameTextStyle: {
            fontSize: 12,
            color: '#666',
          },
          axisLabel: {
            formatter: (value: number) => `${value}${t('time_days')}`,
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0',
            },
          },
        },
        series: [],
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          formatter: formatVehicleModelDealTooltip,
        },
      },
      size: { height: 450 },
      position: { x: 0, y: 0 },
    };
  }

  public updateChartData(baseConfig: ChartConfig, chartData: ChartDataItem[], averageDealCycle: number): Partial<ChartConfig> {
    const modelNames = chartData.map((item) => item.name);

    // 生成柱状图系列配置
    const barSeries = {
      name: t('transactionCycle'),
      type: 'bar',
      data: chartData.map((model) => ({
        name: model.name,
        value: model.value,
        itemStyle: {
          color: model.color || '#5470c6',
        },
      })),
      label: {
        show: true,
        position: 'top',
        formatter: (params: any) => `${params.data.value}天`,
        fontSize: 12,
        color: '#333',
      },
      barWidth: '60%',
    };

    // 生成平均线系列配置
    const averageLineSeries = {
      name: t('averageTransactionCycle'),
      type: 'line',
      data: chartData.map(() => ({
        value: averageDealCycle,
      })),
      lineStyle: {
        color: '#1890ff',
        width: 2,
        type: 'dashed',
      },
      itemStyle: {
        color: '#1890ff',
      },
      symbol: 'none',
      label: {
        show: false,
      },
      markLine: {
        silent: true,
        symbol: 'none',
        label: {
          show: true,
          position: 'end',
          formatter: `${t('average')}：${averageDealCycle}${t('time_days')}`,
          color: '#1890ff',
          fontSize: 12,
          fontWeight: 'bold',
          offset: [-10, 0],
        },
        lineStyle: {
          color: '#1890ff',
          width: 1,
          type: 'dashed',
        },
        data: [
          {
            yAxis: averageDealCycle,
          },
        ],
      },
    };

    return {
      options: {
        ...baseConfig.options,
        // 顶部显示平均成交周期
        graphic: [
          {
            type: 'text',
            left: 'center',
            top: '5%',
            style: {
              text: `${t('averageTransactionCycle')}：${averageDealCycle}${t('time_days')}`,
              fontSize: 14,
              fontWeight: 'bold',
              fill: '#333',
            },
          },
        ],
        xAxis: {
          ...baseConfig.options?.xAxis,
          type: 'category',
          data: modelNames,
        },
        series: [barSeries, averageLineSeries] as any,
        legend: {
          ...baseConfig.options?.legend,
          data: [t('transactionCycle'), t('averageTransactionCycle')],
        },
        tooltip: {
          ...baseConfig.options?.tooltip,
        },
      },
      customProps: {
        ...baseConfig.customProps,
        loading: false,
        isEmpty: chartData.length === 0,
        lastUpdated: new Date().toISOString(),
      },
    };
  }
}

export const vehicleModelDealChartConfigManager = VehicleModelDealChartConfigManager.getInstance();
export const generateBaseVehicleModelDealChartConfig = (): ChartConfig => vehicleModelDealChartConfigManager.generateBaseConfig();
export const updateVehicleModelDealChartData = (
  baseConfig: ChartConfig,
  chartData: ChartDataItem[],
  averageDealCycle: number
): Partial<ChartConfig> => vehicleModelDealChartConfigManager.updateChartData(baseConfig, chartData, averageDealCycle);
export const getVehicleModelDealChartConfigManager = (): VehicleModelDealChartConfigManager => vehicleModelDealChartConfigManager;
export const vehicleModelDealChartConfig: ChartConfig = generateBaseVehicleModelDealChartConfig();
