import { useI18n } from '/@/hooks/web/useI18n';

const { t } = useI18n('common');

/** 线索Overview统计数据配置 */
export const clueOverviewStatisticsConfig = {
  id: 'clueOverview',
  dataSource: 'clueOverview',
  needsAsyncData: true,
  loadingStrategy: 'clue-overview',
  items: [
    {
      id: 'clueAllCount',
      title: t('totalNumberOfClues'),
      total: 0,
      core: true,
    },
    {
      id: 'clueValidCount',
      title: t('totalNumberOfEffectiveClues'),
      total: 0,
      core: false,
    },
    {
      id: 'clueDealCount',
      title: t('clueTradingVolume'),
      total: 0,
      core: false,
    },
    {
      id: 'clueFollowPercent',
      title: t('leadFollowUpRate'),
      total: 0,
      core: false,
    },
    {
      id: 'clueValidPercent',
      title: t('cluesAreEfficient'),
      total: 0,
      core: false,
    },
    {
      id: 'clueWinPercent',
      title: t('clueWinRate'),
      total: 0,
      core: false,
    },
  ],
};

/** 线索转化统计数据配置 */
export const leadConversionStatisticsConfig = {
  id: 'leadConversion',
  dataSource: 'leadConversion',
  needsAsyncData: true,
  loadingStrategy: 'lead-conversion',
  items: [
    {
      id: 'overallConversionRate',
      title: t('overallConversionRate'),
      total: 0,
      core: true,
    },
    {
      id: 'averageConversionCycle',
      title: t('averageConversionCycle'),
      total: 0,
      core: false,
    },
    {
      id: 'numberOfBusCables',
      title: t('numberOfBusCables'),
      total: 0,
      core: false,
    },
    {
      id: 'numberOfDeals',
      title: t('numberOfDeals'),
      total: 0,
      core: false,
    },
    {
      id: 'clueDefeatRate',
      title: t('clueDefeatRate'),
      total: 0,
      core: false,
    },
  ],
};

/** 用户分析统计数据配置 */
export const userAnalysisStatisticsConfig = {
  id: 'userAnalysis',
  dataSource: 'userAnalysis',
  needsAsyncData: true,
  loadingStrategy: 'user-analysis',
  items: [
    {
      // 用户总量
      id: 'userCountTotal',
      title: '用户总量',
      total: 0,
    },
    {
      // 新增用户
      id: 'addNewUser',
      title: '新增用户',
      total: 0,
    },
    {
      // 注销用户
      id: 'cancelUser',
      title: '注销用户',
      total: 0,
    },
  ],
};
