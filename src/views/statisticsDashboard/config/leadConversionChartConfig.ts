/**
 * 线索转化分析图表配置管理器
 * 基于统一架构的单例配置管理器，负责生成和更新线索转化分析图表配置
 */

import { useI18n } from '/@/hooks/web/useI18n';
import type { ChartConfig, ChartDataItem } from '../types/statisticDashboard';
import { createTooltipFormatter } from '../utils';

const { t } = useI18n('common');

/**
 * 线索转化阶段配置接口
 */
export interface LeadConversionStageConfig {
  /** 阶段名称 */
  name: string;
  /** 阶段数量 */
  value: number;
  /** 转化率 */
  conversionRate?: number;
  /** 阶段颜色 */
  color?: string;
}

/**
 * 线索转化分析数据接口
 */
export interface LeadConversionAnalysisData {
  /** 转化阶段数据 */
  stages: LeadConversionStageConfig[];
  /** 总线索数 */
  totalLeads: number;
  /** 最终转化率 */
  finalConversionRate: string;
}

/**
 * 线索转化分析图表 tooltip
 */
const tooltipFormatter = createTooltipFormatter({
  showPercentage: false,
  extraInfoProvider: (_itemName: string, params: any[]) => {
    if (!params || params.length === 0) return '';
    const param = params[0];
    if (!param.data || !param.data.conversionRate) return '';
    return `<div style="color: #666; font-size: 12px;">${t('conversionRate')}：${param.data.conversionRate}%</div>`;
  },
  valueFormatter: (value: number) => value.toString(),
});

/**
 * 线索转化分析图表配置管理器
 * 单例模式，负责管理线索转化分析图表的配置生成和更新
 */
export class LeadConversionChartConfigManager {
  private static instance: LeadConversionChartConfigManager;
  private readonly loadingStrategy = 'lead-conversion-analysis';

  private constructor() {
    // 私有构造函数，确保单例模式
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): LeadConversionChartConfigManager {
    if (!LeadConversionChartConfigManager.instance) {
      LeadConversionChartConfigManager.instance = new LeadConversionChartConfigManager();
    }
    return LeadConversionChartConfigManager.instance;
  }

  /**
   * 生成基础图表配置
   */
  public generateBaseConfig(): ChartConfig {
    return {
      loadingStrategy: this.loadingStrategy,
      id: this.loadingStrategy,
      type: 'funnel',
      title: t('clueConversionAnalysis'),
      dataSource: 'leadConversionAnalysis',
      customProps: {
        needsAsyncData: true,
      },
      options: {
        // 禁用ECharts内置标题，使用自定义标题
        title: {
          show: false,
        },
        tooltip: {
          trigger: 'item',
          formatter: tooltipFormatter,
        },
        legend: {
          show: true,
          data: [],
          bottom: '5%',
          left: 'center',
          type: 'scroll',
        },
        series: [
          {
            name: t('leadConversion'),
            type: 'funnel',
            left: '10%',
            top: 60,
            bottom: 60,
            width: '80%',
            min: 0,
            max: 100,
            minSize: '0%',
            maxSize: '100%',
            sort: 'none', // 保持原始数据顺序，不按数值大小排序
            gap: 2,
            label: {
              show: true,
              position: 'inside',
              formatter: (params: any) => {
                return `${params.name}\n${params.value}`;
              },
              fontSize: 12,
              color: '#fff',
            },
            labelLine: {
              length: 10,
              lineStyle: {
                width: 1,
                type: 'solid',
              },
            },
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 1,
            },
            emphasis: {
              label: {
                fontSize: 14,
              },
            },
            data: [],
          },
        ],
      },
    };
  }

  /**
   * 更新图表数据
   * @param baseConfig 基础配置
   * @param chartData 图表数据
   * @returns 更新后的配置
   */
  public updateChartData(baseConfig: ChartConfig, chartData: ChartDataItem[]): Partial<ChartConfig> {
    if (!chartData || chartData.length === 0) {
      return {
        ...baseConfig,
        options: {
          ...baseConfig.options,
          legend: {
            ...baseConfig.options?.legend,
            data: [],
          },
          series: [
            {
              ...baseConfig.options?.series?.[0],
              data: [],
            },
          ],
        },
      };
    }

    // 转换数据格式为ECharts漏斗图所需格式
    const funnelData = chartData.map((item) => ({
      name: item.name,
      value: item.value,
      conversionRate: item.conversionRate,
      itemStyle: {
        color: item.color || '#1890ff',
      },
    }));

    // 计算最大值用于漏斗图缩放
    const values = chartData.map((item) => (typeof item.value === 'number' ? item.value : 0));
    const maxValue = Math.max(...values);

    return {
      ...baseConfig,
      options: {
        ...baseConfig.options,
        legend: {
          ...baseConfig.options?.legend,
          data: chartData.map((item) => item.name),
        },
        series: [
          {
            ...baseConfig.options?.series?.[0],
            max: maxValue,
            data: funnelData,
          },
        ],
      },
    };
  }
}

// 导出单例实例和便捷方法
export const leadConversionChartConfigManager = LeadConversionChartConfigManager.getInstance();
export const generateBaseLeadConversionChartConfig = (): ChartConfig => leadConversionChartConfigManager.generateBaseConfig();
export const updateLeadConversionChartData = (baseConfig: ChartConfig, chartData: ChartDataItem[]): Partial<ChartConfig> =>
  leadConversionChartConfigManager.updateChartData(baseConfig, chartData);
export const getLeadConversionChartConfigManager = () => leadConversionChartConfigManager;
export const leadConversionChartConfig: ChartConfig = generateBaseLeadConversionChartConfig();
export default leadConversionChartConfigManager;
