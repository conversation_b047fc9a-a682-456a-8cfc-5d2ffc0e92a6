import { useI18n } from '/@/hooks/web/useI18n';
import type { ChartConfig, ChartDataItem } from '../types/statisticDashboard';
import { generateChartDataItem } from '../utils';

const { t } = useI18n('common');

// 专用tooltip格式化函数
const formatFirstResponseTimeoutTooltip = (params: any) => {
  if (!params || !Array.isArray(params) || params.length === 0) return '';

  const axisValue = params[0].axisValue;
  const dataItem = params[0].data;

  // 从数据项中提取扩展数据
  const timeoutCount = dataItem?.timeoutCount || 0;
  const withinTimeCount = dataItem?.withinTimeCount || 0;
  const totalCount = dataItem?.totalCount || timeoutCount + withinTimeCount;
  const timeoutPercent = dataItem?.timeoutPercent || (totalCount > 0 ? ((timeoutCount / totalCount) * 100).toFixed(1) : '0.0');

  return [
    `<div style="font-weight: bold;">${axisValue}</div>`,
    `${t('timeoutClue')}：${timeoutCount}`,
    `${t('numberOfBusCables')}：${totalCount}`,
    `${t('timeoutRate')}： ${timeoutPercent} %`,
  ].join('<br/>');
};

/**
 * 首次响应超时图表配置管理器
 */
export class FirstResponseTimeoutChartConfigManager {
  private static instance: FirstResponseTimeoutChartConfigManager;
  private loadingStrategy = 'firstResponseTimeout';

  private constructor() {}

  public static getInstance(): FirstResponseTimeoutChartConfigManager {
    if (!FirstResponseTimeoutChartConfigManager.instance) {
      FirstResponseTimeoutChartConfigManager.instance = new FirstResponseTimeoutChartConfigManager();
    }
    return FirstResponseTimeoutChartConfigManager.instance;
  }

  /**
   * 生成基础配置
   * 单数据源配置，无切换标题功能
   */
  public generateBaseConfig(): ChartConfig {
    const title = t('firstResponseTimeout');

    // 面积图系列 - 超过72小时的线索
    const timeoutAreaSeries = {
      name: t('timeoutCluesMoreThanHours', { hours: 72 }),
      type: 'line' as const,
      stack: 'total',
      smooth: true,
      areaStyle: {
        color: {
          type: 'linear' as const,
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(255, 99, 132, 0.8)' },
            { offset: 1, color: 'rgba(255, 99, 132, 0.3)' },
          ],
        },
      },
      lineStyle: {
        color: '#ff6384',
        width: 2,
      },
      itemStyle: {
        color: '#ff6384',
      },
      symbol: 'circle',
      symbolSize: 6,
      data: [],
    };

    // 面积图系列 - 72小时内的线索
    const withinTimeAreaSeries = {
      name: t('timeoutCluesWithinHours', { hours: 72 }),
      type: 'line' as const,
      stack: 'total',
      smooth: true,
      areaStyle: {
        color: {
          type: 'linear' as const,
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(75, 192, 192, 0.8)' },
            { offset: 1, color: 'rgba(75, 192, 192, 0.3)' },
          ],
        },
      },
      lineStyle: {
        color: '#4bc0c0',
        width: 2,
      },
      itemStyle: {
        color: '#4bc0c0',
      },
      symbol: 'circle',
      symbolSize: 6,
      data: [],
    };

    const series = [timeoutAreaSeries, withinTimeAreaSeries];

    return {
      id: 'firstResponseTimeout',
      type: 'line',
      title,
      dataSource: 'firstResponseTimeout',
      loadingStrategy: this.loadingStrategy,
      customProps: {
        needsAsyncData: true,
        switchable: false, // 单数据源，无切换功能
        currentDataSource: 'firstResponseTimeout',
        supportedDataSources: ['firstResponseTimeout'],
      },
      options: {
        // 禁用ECharts内置标题，使用自定义标题
        title: {
          show: false,
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985',
            },
          },
          formatter: formatFirstResponseTimeoutTooltip,
        },
        legend: {
          data: [t('timeoutCluesMoreThanHours', { hours: 72 }), t('timeoutCluesWithinHours', { hours: 72 })],
          bottom: '5%',
          textStyle: {
            fontSize: 12,
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '15%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: [],
            axisLabel: {
              interval: 0,
              rotate: 30,
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            name: t('numberOfBusCables'),
            nameTextStyle: {
              color: '#666',
              fontSize: 12,
            },
            axisLabel: {
              formatter: '{value}',
            },
            splitLine: {
              lineStyle: {
                color: '#f0f0f0',
              },
            },
          },
        ],
        series: series as any,
      },
      size: { height: 500 },
    };
  }

  /**
   * 更新图表数据
   * @param baseConfig 基础配置
   * @param chartData 图表数据
   */
  public updateChartData(baseConfig: ChartConfig, chartData: ChartDataItem[]): Partial<ChartConfig> {
    const months = chartData.map((item) => item.name);

    // 面积图系列 - 超过72小时的线索
    const timeoutAreaSeries = {
      name: t('timeoutCluesMoreThanHours', { hours: 72 }),
      type: 'line' as const,
      stack: 'total',
      smooth: true,
      areaStyle: {
        color: {
          type: 'linear' as const,
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(255, 99, 132, 0.8)' },
            { offset: 1, color: 'rgba(255, 99, 132, 0.3)' },
          ],
        },
      },
      lineStyle: {
        color: '#ff6384',
        width: 2,
      },
      itemStyle: {
        color: '#ff6384',
      },
      symbol: 'circle',
      symbolSize: 6,
      data: chartData.map((item) => {
        // 从 customData 中提取超时数据
        const timeoutCount = (item as any).timeoutCount || 0;
        return generateChartDataItem(item.name, timeoutCount, '0', {
          timeoutCount: (item as any).timeoutCount,
          withinTimeCount: (item as any).withinTimeCount,
          totalCount: (item as any).totalCount,
          timeoutPercent: (item as any).timeoutPercent,
          withinTimePercent: (item as any).withinTimePercent,
        });
      }),
    };

    // 面积图系列 - 72小时内的线索
    const withinTimeAreaSeries = {
      name: t('timeoutCluesWithinHours', { hours: 72 }),
      type: 'line' as const,
      stack: 'total',
      smooth: true,
      areaStyle: {
        color: {
          type: 'linear' as const,
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(75, 192, 192, 0.8)' },
            { offset: 1, color: 'rgba(75, 192, 192, 0.3)' },
          ],
        },
      },
      lineStyle: {
        color: '#4bc0c0',
        width: 2,
      },
      itemStyle: {
        color: '#4bc0c0',
      },
      symbol: 'circle',
      symbolSize: 6,
      data: chartData.map((item) => {
        // 从 customData 中提取正常数据
        const withinTimeCount = (item as any).withinTimeCount || 0;
        return generateChartDataItem(item.name, withinTimeCount, '0', {
          timeoutCount: (item as any).timeoutCount,
          withinTimeCount: (item as any).withinTimeCount,
          totalCount: (item as any).totalCount,
          timeoutPercent: (item as any).timeoutPercent,
          withinTimePercent: (item as any).withinTimePercent,
        });
      }),
    };

    // 总数量折线系列
    const totalCountLineSeries = {
      name: t('numberOfClues'),
      type: 'line' as const,
      smooth: true,
      lineStyle: {
        color: '#8B5CF6',
        width: 2,
        // type: '',
      },
      itemStyle: {
        color: '#8B5CF6',
        borderWidth: 2,
        borderColor: '#fff',
      },
      symbol: 'diamond',
      symbolSize: 8,
      data: chartData.map((item) => {
        // 显示总数量
        const totalCount = (item as any).totalCount || 0;
        return generateChartDataItem(item.name, totalCount, '0', {
          timeoutCount: (item as any).timeoutCount,
          withinTimeCount: (item as any).withinTimeCount,
          totalCount: (item as any).totalCount,
          timeoutPercent: (item as any).timeoutPercent,
          withinTimePercent: (item as any).withinTimePercent,
        });
      }),
      z: 10, // 确保总数量线在最上层
    };

    return {
      options: {
        ...baseConfig.options,
        xAxis: [
          {
            ...baseConfig.options?.xAxis,
            type: 'category',
            data: months,
            axisLabel: {
              interval: 0,
              rotate: 30,
            },
          },
        ],
        yAxis: [
          {
            type: 'value' as const,
            name: t('numberOfBusCables'),
            nameTextStyle: {
              color: '#666',
              fontSize: 12,
            },
            axisLabel: {
              formatter: '{value}',
            },
            splitLine: {
              lineStyle: {
                color: '#f0f0f0',
              },
            },
          },
        ],
        series: [timeoutAreaSeries, withinTimeAreaSeries, totalCountLineSeries] as any,
        legend: {
          ...baseConfig.options?.legend,
          data: [t('timeoutCluesMoreThanHours', { hours: 72 }), t('timeoutCluesWithinHours', { hours: 72 })],
        },
      },
      customProps: {
        ...baseConfig.customProps,
        loading: false,
        needsAsyncData: true,
        isEmpty: chartData.length === 0,
        lastUpdated: new Date().toISOString(),
      },
    };
  }

  /**
   * 获取标题（单数据源，无切换功能）
   */
  public static getTitleByDataSource(_dataSource: string): string {
    return t('firstResponseTimeout');
  }
}

// 导出单例实例和便捷方法
export const firstResponseTimeoutChartConfigManager = FirstResponseTimeoutChartConfigManager.getInstance();

export const generateBaseFirstResponseTimeoutChartConfig = (): ChartConfig => firstResponseTimeoutChartConfigManager.generateBaseConfig();

export const updateFirstResponseTimeoutChartData = (baseConfig: ChartConfig, chartData: ChartDataItem[]): Partial<ChartConfig> =>
  firstResponseTimeoutChartConfigManager.updateChartData(baseConfig, chartData);

export const getFirstResponseTimeoutChartConfigManager = (): FirstResponseTimeoutChartConfigManager => firstResponseTimeoutChartConfigManager;

export const firstResponseTimeoutChartConfig: ChartConfig = generateBaseFirstResponseTimeoutChartConfig();
