/**
 * 统计数据加载策略系统
 * 基于统一策略模式架构，实现统计数据的统一加载和管理
 */

import type { StatisticsItem, StatisticsConfig } from '../types/statisticDashboard';
import type { commonQueryParams, ClueOverViewResponse, ClueConversionDataResponse, ClueOverViewCountDto, UserAnalysisDataResp } from '../api/index';
import { BaseStrategy, type IBaseStrategy, BaseSingletonStrategyFactory } from './BaseStrategy';
import { createStatisticsDataLoadingStrategy } from './StrategyDependencyInjection';

// ========== 接口定义 ==========

/**
 * 统计数据加载策略接口
 * 继承通用策略接口，添加统计数据特有的方法
 */
export interface IStatisticsDataLoadingStrategy extends IBaseStrategy<StatisticsConfig, StatisticsItem[]> {
  /** 支持的数据源类型 */
  readonly supportedDataSources: string[];
  /**
   * 刷新统计数据
   * @param config 统计配置
   * @param params 查询参数
   */
  refreshStatisticsData(config: StatisticsConfig, params?: commonQueryParams, context?: any): Promise<StatisticsItem[]>;

  /**
   * 获取缓存的统计数据
   */
  getCachedStatisticsData(configId: string): StatisticsItem[];

  /**
   * 检查是否有缓存数据
   */
  hasCachedData(configId: string): boolean;

  /**
   * 检查是否正在加载
   */
  isLoading(configId: string): boolean;
}

// ========== 统计数据加载策略抽象基类 ==========

/**
 * 统计数据加载策略抽象基类
 * 继承通用策略基类，实现统计数据特有的逻辑
 */
export abstract class BaseStatisticsDataLoadingStrategy
  extends BaseStrategy<StatisticsConfig, StatisticsItem[]>
  implements IStatisticsDataLoadingStrategy
{
  // 抽象属性 - 子类必须实现
  abstract readonly supportedDataSources: string[];

  constructor() {
    super();
  }

  /**
   * 获取配置ID
   */
  protected getConfigId(config: StatisticsConfig): string {
    return config.id;
  }

  /**
   * 抽象方法 - 子类必须实现
   */
  protected abstract transformData(apiData: any, config: StatisticsConfig): Promise<StatisticsItem[]>;

  /**
   * 支持的类型（实现基类要求）
   */
  get supportedTypes(): string[] {
    return this.supportedDataSources;
  }

  /**
   * 配置验证
   */
  validateConfig(config: StatisticsConfig): boolean {
    return !!(config.id && this.supportedDataSources.includes(config.dataSource));
  }

  /**
   * 加载统计数据
   */
  async loadStatisticsData(config: StatisticsConfig, params?: commonQueryParams): Promise<StatisticsItem[]> {
    return this.loadData(config, params);
  }

  /**
   * 刷新统计数据
   */
  async refreshStatisticsData(config: StatisticsConfig, params?: commonQueryParams): Promise<StatisticsItem[]> {
    return this.refreshData(config, params);
  }

  /**
   * 获取缓存的统计数据
   */
  getCachedStatisticsData(configId: string): StatisticsItem[] {
    const cachedData = this.getCachedData(configId);
    return (cachedData as StatisticsItem[]) || [];
  }

  /**
   * 检查是否有缓存数据
   */
  hasCachedData(configId: string): boolean {
    const cachedData = this.getCachedStatisticsData(configId);
    return Array.isArray(cachedData) && cachedData.length > 0;
  }

  /**
   * 检查是否正在加载
   */
  isLoading(configId: string): boolean {
    return this.getLoadingState(configId);
  }
}

// ========== 具体策略实现 ==========

/**
 * 线索总览统计数据加载策略
 */
export class ClueOverviewStatisticsStrategy extends BaseStatisticsDataLoadingStrategy {
  readonly strategyType = 'clue-overview';
  readonly supportedDataSources = ['clueOverview', 'clue-overview'];

  protected async fetchApiData(params: commonQueryParams): Promise<ClueOverViewResponse> {
    // 🔥 动态导入API，避免循环依赖
    const { queryClueOverView } = await import('../api/index');
    return await queryClueOverView(params);
  }

  protected async transformData(apiData: ClueOverViewResponse, config: StatisticsConfig): Promise<StatisticsItem[]> {
    // console.log('🔄 转换线索总览统计数据:', apiData);
    const { transformStatisticsData, clueOverviewDataExtractor } = await import('../utils/dataTransform');

    // 定义API字段到统计项的映射
    const fieldMapping: Record<string, string> = {
      clueAllCount: 'clueAllCount', // 全量线索总量
      clueValidCount: 'clueValidCount', // 有效线索总量
      clueDealCount: 'clueDealCount', // 线索成交量
      clueFollowPercent: 'clueFollowPercent', // 线索跟进率
      clueValidPercent: 'clueValidPercent', // 线索有效率
      clueWinPercent: 'clueWinPercent', // 线索战胜率
    };

    return transformStatisticsData(apiData, config, fieldMapping, clueOverviewDataExtractor);
  }
}

/**
 * 线索转化统计数据加载策略
 * 负责处理线索转化相关的统计数据加载和转换
 */
export class LeadConversionStatisticsStrategy extends BaseStatisticsDataLoadingStrategy {
  readonly strategyType = 'lead-conversion';
  readonly supportedDataSources = ['leadConversion', 'lead-conversion'];

  protected async fetchApiData(params: commonQueryParams): Promise<ClueConversionDataResponse> {
    const { queryClueConversionData } = await import('../api/index');
    return await queryClueConversionData(params);
  }

  protected async transformData(apiData: ClueConversionDataResponse, config: StatisticsConfig): Promise<StatisticsItem[]> {
    const { transformClueConversionDataToStatistics } = await import('../utils/dataTransform');
    return transformClueConversionDataToStatistics(apiData, config);
  }
}

/**
 * 用户分析统计数据加载策略
 */
export class UserAnalysisStatisticsStrategy extends BaseStatisticsDataLoadingStrategy {
  readonly strategyType = 'user-analysis';
  readonly supportedDataSources = ['userAnalysis', 'user-analysis'];

  protected async fetchApiData(params: commonQueryParams): Promise<UserAnalysisDataResp> {
    const { queryUserTotalQuantity, queryUserNewQuantity, queryUserLogoffQuantity } = await import('../api/index');
    const [userTotal, newQuantity, userLogoff] = await Promise.all<ClueOverViewCountDto>([
      queryUserTotalQuantity(params),
      queryUserNewQuantity(params),
      queryUserLogoffQuantity(params),
    ]);
    return {
      userTotal,
      newQuantity,
      userLogoff,
    };
  }

  protected async transformData(
    apiData: { userTotal: ClueOverViewCountDto; newQuantity: ClueOverViewCountDto; userLogoff: ClueOverViewCountDto },
    config: StatisticsConfig
  ): Promise<StatisticsItem[]> {
    const { transformUserAnalysisDataToStatistics } = await import('../utils/dataTransform');
    return transformUserAnalysisDataToStatistics(apiData, config);
  }
}

// ========== 策略工厂 ==========

/**
 * 统计数据加载策略工厂
 * 🔥 优化：继承 BaseSingletonStrategyFactory，使用统一的单例策略管理
 */
export class StatisticsDataLoadingStrategyFactory extends BaseSingletonStrategyFactory<IStatisticsDataLoadingStrategy, StatisticsConfig> {
  private constructor() {
    super('StatisticsDataLoadingStrategyFactory');
  }

  /**
   * 获取工厂单例实例
   */
  public static getInstance(): StatisticsDataLoadingStrategyFactory {
    return this.getOrCreateInstance('StatisticsDataLoadingStrategyFactory', () => new StatisticsDataLoadingStrategyFactory());
  }

  /**
   * 获取策略 - 实现基类的抽象方法
   */
  getStrategy(config: StatisticsConfig): IStatisticsDataLoadingStrategy {
    const strategy = this.getStrategyByType(config.loadingStrategy);
    if (!strategy) {
      throw new Error('未找到指定的统计数据加载策略');
    }
    return strategy;
  }

  /**
   * 静态方法：获取策略
   */
  static getStrategy(config: StatisticsConfig): IStatisticsDataLoadingStrategy {
    const instance = this.getInstance();
    return instance.getStrategy(config);
  }

  /**
   * 静态方法：注册策略
   */
  static registerStrategy(strategy: IStatisticsDataLoadingStrategy): void {
    StatisticsDataLoadingStrategyFactory.getInstance().registerStrategy(strategy);
  }

  /**
   * 静态方法：获取所有策略
   */
  static getAllStrategies(): IStatisticsDataLoadingStrategy[] {
    return StatisticsDataLoadingStrategyFactory.getInstance().getAllStrategies();
  }

  /**
   * 静态方法：清除所有策略（用于测试）
   */
  static clearStrategies(): void {
    StatisticsDataLoadingStrategyFactory.getInstance().clearStrategies();
  }
}

// ========== 自动注册策略 ==========

// 🔥 优化：使用依赖注入自动注册策略
StatisticsDataLoadingStrategyFactory.registerStrategy(createStatisticsDataLoadingStrategy(ClueOverviewStatisticsStrategy));
StatisticsDataLoadingStrategyFactory.registerStrategy(createStatisticsDataLoadingStrategy(LeadConversionStatisticsStrategy));
StatisticsDataLoadingStrategyFactory.registerStrategy(createStatisticsDataLoadingStrategy(UserAnalysisStatisticsStrategy));

console.log('📊 统计数据加载策略系统初始化完成（使用依赖注入）');
