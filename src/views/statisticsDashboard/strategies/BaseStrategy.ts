/**
 * 统一策略模式基础架构
 * 提供所有数据加载策略的通用接口、装饰器和抽象基类
 * 解决策略模式中的代码冗余问题，实现高度复用的架构设计
 */

import { reactive } from 'vue';
import type { commonQueryParams } from '../api';
import { type UnifiedQueryParamsManager } from '../hooks/useUnifiedQueryParams';
import { message } from 'ant-design-vue';
import { useGlobalDataSourceManager, type IDataSourceManager } from '../hooks/useDataSourceManager';
import { getStrategyDependencyContainer } from './StrategyDependencyInjection';

// ========== 通用接口定义 ==========

/**
 * 通用策略基础接口
 * @template TConfig 配置类型
 * @template TData 数据类型
 */
export interface IBaseStrategy<TConfig = any, TData = any> {
  /** 策略类型标识 */
  readonly strategyType: string;

  /** 支持的类型列表（图表类型、数据源类型等） */
  readonly supportedTypes: string[];

  /**
   * 加载数据
   * @param config 配置对象
   * @param context 上下文信息（如 groupId 等）
   */
  loadData(config: TConfig, context?: any): Promise<TData>;

  /**
   * 刷新数据
   * @param config 配置对象
   * @param context 上下文信息（如 groupId 等）
   */
  refreshData(config: TConfig, context?: any): Promise<TData>;

  /**
   * 验证配置
   * @param config 配置对象
   */
  validateConfig(config: TConfig): boolean;

  /**
   * 获取加载状态
   * @param configId 配置ID
   */
  getLoadingState(configId: string): boolean;
}

/**
 * 策略工厂基础接口
 */
export interface IBaseStrategyFactory<TStrategy extends IBaseStrategy, TConfig = any> {
  /**
   * 注册策略
   * @param strategy 策略实例
   */
  registerStrategy(strategy: TStrategy): void;

  /**
   * 获取策略
   * @param config 配置对象
   */
  getStrategy(config: TConfig): TStrategy | null;

  /**
   * 获取所有策略
   */
  getAllStrategies(): TStrategy[];

  /**
   * 清除所有策略
   */
  clearStrategies(): void;
}

// ========== 通用抽象基类 ==========

/**
 * 抽象基类 - 实现通用策略模板方法模式
 * @template TConfig 配置类型
 * @template TData 数据类型
 */
export abstract class BaseStrategy<TConfig = any, TData = any> implements IBaseStrategy<TConfig, TData> {
  protected loadingStates = reactive<Record<string, boolean>>({});
  protected dataSourceManager: IDataSourceManager;

  constructor() {
    // 使用全局数据源管理器
    this.dataSourceManager = useGlobalDataSourceManager();
  }

  /**
   * 从依赖容器获取查询参数管理器
   */
  protected getQueryParamsManager(): UnifiedQueryParamsManager | undefined {
    try {
      const container = getStrategyDependencyContainer();
      return container.getQueryParamsManager();
    } catch (error) {
      console.warn('🔥 无法从依赖容器获取查询参数管理器:', error);
      return undefined;
    }
  }

  // 抽象属性 - 子类必须实现
  abstract readonly strategyType: string;
  abstract readonly supportedTypes: string[];

  // 抽象方法 - 子类必须实现
  protected abstract fetchApiData(params: commonQueryParams, config: TConfig): Promise<any>;
  protected abstract transformData(apiData: any, config: TConfig): Promise<TData>;
  protected abstract getConfigId(config: TConfig): string;

  /**
   * 🔥 模板方法：标准数据加载流程
   */
  async loadData(config: TConfig, context?: any): Promise<TData> {
    const configId = this.getConfigId(config);

    // 前置验证
    if (!this.validateConfig(config)) {
      throw new Error(`配置验证失败: ${configId}`);
    }

    // 设置加载状态
    this.setLoadingState(configId, true);

    try {
      console.log(`🚀 开始加载数据 [${this.strategyType}]: ${configId}`);

      // 执行数据加载流程
      const result = await this.performDataLoading(config, context);

      // 存储数据到数据源管理器
      await this.storeDataToManager(configId, result, config);

      console.log(`✅ 数据加载成功: ${configId}`);
      return result;
    } catch (error: any) {
      console.error(`❌ 数据加载失败 [${this.strategyType}]: ${configId}`, error);
      throw error;
    } finally {
      this.setLoadingState(configId, false);
    }
  }

  /**
   * 执行数据加载的核心流程
   */
  protected async performDataLoading(config: TConfig, context?: any): Promise<TData> {
    // 🔥 使用查询参数管理器获取参数
    const queryParamsManager = this.getQueryParamsManager();

    if (!queryParamsManager) {
      throw new Error(`🚨 ${this.strategyType} - 查询参数管理器不可用，请确保在主组件中正确注入`);
    }

    const finalParams = queryParamsManager.getFinalParams(context?.groupId);
    // console.log(`🚀 ${this.strategyType} - 从管理器获取参数:`, finalParams);
    // if (context?.groupId) {
    //   console.log(`📋 ${this.strategyType} - 使用局部参数作用域 [${context.groupId}]`);
    // } else {
    //   console.log(`📋 ${this.strategyType} - 使用全局参数作用域`);
    // }

    // 直接调用API
    const apiData = await this.fetchApiData(finalParams, config);

    // 数据转换
    const transformedData = await this.transformData(apiData, config);

    // 数据验证
    this.validateData(transformedData);

    return transformedData;
  }

  /**
   * 刷新数据（复用加载逻辑）
   */
  async refreshData(config: TConfig, context?: any): Promise<TData> {
    console.log(`🔄 刷新数据: ${this.getConfigId(config)}`);
    return this.loadData(config, context);
  }

  /**
   * 默认配置验证实现 - 子类可以覆盖
   */
  validateConfig(config: TConfig): boolean {
    const configId = this.getConfigId(config);
    return !!configId;
  }

  /**
   * 数据验证 - 子类可以覆盖
   */
  protected validateData(data: TData): void {
    if (data === null || data === undefined) {
      throw new Error('转换后的数据不能为空');
    }
  }

  /**
   * 错误处理 - 子类可以覆盖
   */
  protected handleLoadingError(error: any, _config: TConfig): void {
    const errorMessage = error instanceof Error ? error.message : String(error);
    message.error(`数据加载失败: ${errorMessage}`);
  }

  /**
   * 存储数据到数据源管理器 - 子类可以覆盖
   */
  protected async storeDataToManager(configId: string, data: TData, _config: TConfig): Promise<void> {
    try {
      this.dataSourceManager.setDataSource(configId, data as any, {
        name: configId,
        description: `${this.strategyType} 数据: ${configId}`,
        category: 'strategy' as any,
        type: 'strategy' as any,
        dataType: 'any' as any,
      });
    } catch (error) {
      console.warn(`数据存储到管理器失败: ${configId}`, error);
    }
  }

  /**
   * 从数据源管理器获取缓存数据
   */
  getCachedData(configId: string): TData {
    return this.dataSourceManager.getDataSource(configId) as TData;
  }

  /**
   * 检查是否有缓存数据
   */
  hasCachedData(configId: string): boolean {
    const cachedData = this.getCachedData(configId);
    return cachedData !== null && cachedData !== undefined;
  }

  /**
   * 加载状态管理
   */
  protected setLoadingState(configId: string, loading: boolean): void {
    this.loadingStates[configId] = loading;
  }

  /**
   * 获取加载状态
   */
  getLoadingState(configId: string): boolean {
    return this.loadingStates[configId] || false;
  }
}

// ========== 通用策略工厂抽象类 ==========

/**
 * 抽象策略工厂基类
 * @template TStrategy 策略类型
 * @template TConfig 配置类型
 */
export abstract class BaseStrategyFactory<TStrategy extends IBaseStrategy, TConfig = any> implements IBaseStrategyFactory<TStrategy, TConfig> {
  protected strategies: Map<string, TStrategy> = new Map();
  protected readonly factoryName: string;

  constructor(factoryName: string) {
    this.factoryName = factoryName;
  }

  /**
   * 注册策略
   */
  registerStrategy(strategy: TStrategy): void {
    this.strategies.set(strategy.strategyType, strategy);
    console.log(`📝 ${this.factoryName} - 注册策略: ${strategy.strategyType}`);
  }

  /**
   * 获取策略 - 子类必须实现具体的匹配逻辑
   */
  abstract getStrategy(config: TConfig): TStrategy | null;

  /**
   * 获取所有策略
   */
  getAllStrategies(): TStrategy[] {
    return Array.from(this.strategies.values());
  }

  /**
   * 清除所有策略
   */
  clearStrategies(): void {
    this.strategies.clear();
    console.log('1111111111111');
    console.log(`🧹 ${this.factoryName} - 清除所有策略`);
  }

  /**
   * 根据策略类型获取策略
   */
  protected getStrategyByType(strategyType: string): TStrategy | null {
    return this.strategies.get(strategyType) || null;
  }
}

/**
 * 单例策略工厂基类 - 提供单例模式支持
 */
export abstract class BaseSingletonStrategyFactory<TStrategy extends IBaseStrategy, TConfig = any> extends BaseStrategyFactory<TStrategy, TConfig> {
  private static instances: Map<string, any> = new Map();

  /**
   * 受保护的构造函数，防止外部直接实例化
   */
  protected constructor(factoryName: string) {
    super(factoryName);
  }

  /**
   * 获取或创建单例实例的通用方法
   */
  protected static getOrCreateInstance<T>(className: string, createFn: () => T): T {
    if (!BaseSingletonStrategyFactory.instances.has(className)) {
      console.log(`🏭 创建 ${className} 单例实例`);
      BaseSingletonStrategyFactory.instances.set(className, createFn());
    }
    return BaseSingletonStrategyFactory.instances.get(className) as T;
  }

  /**
   * 清除所有单例实例（主要用于测试）
   */
  public static clearAllInstances(): void {
    BaseSingletonStrategyFactory.instances.clear();
    console.log('🧹 已清除所有单例工厂实例');
  }
}

// ========== 导出类型和工具 ==========

/**
 * 策略配置基础接口
 */
export interface BaseStrategyConfig {
  id: string;
  type?: string;
  strategyType?: string;
  [key: string]: any;
}

/**
 * 策略数据项基础接口
 */
export interface BaseDataItem {
  [key: string]: any;
}
