/**
 * 图表数据加载策略系统
 * 基于统一策略模式架构，实现图表数据的加载和管理
 */

import type { ChartConfig, ChartDataItem } from '../types/statisticDashboard';
import type { commonQueryParams } from '../api';
import { BaseStrategy, type IBaseStrategy, BaseSingletonStrategyFactory } from './BaseStrategy';
import { createChartDataLoadingStrategy } from './StrategyDependencyInjection';
import { ClueSourceChartConfigManager } from '../config/clueSourceChartConfig';
import { FollowUpStatusChartConfigManager } from '../config/followUpStatusChartConfig';
import { FirstResponseTimeoutChartConfigManager } from '../config/firstResponseTimeoutChartConfig';
import { queryClueValidStatus, type ClueValidStatusResponse } from '../api';
import { ClueValidStatusEnum } from '../config/followUpStatusChartConfig';
import { queryClueValidFirstFollowTime, type ClueValidFollowTimeResponse } from '../api';
import { queryClueAllFirstRespOverTime, type ClueAllFirstRespOverTimeResponse } from '../api';
import { queryClueConversionDataAnalyzeByStatus, type ClueConversionDataAnalyzeByStatusResponse } from '../api';
import type { ClueConversionDataAnalyzeByModelResponse, UserAnalysisChartDataResponse } from '../api';
import { LeadConversionChartConfigManager } from '../config/leadConversionChartConfig';
import { transformClueConversionDataAnalyzeByStatusToChartData, transformUserAnalysisChartDataToChartData } from '../utils/dataTransform';

// ========== 图表数据加载策略接口 ==========

/**
 * 数据源切换信息接口
 */
export interface DataSourceSwitchInfo {
  /** 新数据源对应的标题 */
  newTitle?: string;
  /** 备选数据源对应的标题 */
  alternativeTitle?: string;
  /** 备选数据源的键名 */
  alternativeDataSource?: string;
}

/**
 * 图表数据加载策略接口
 * 继承通用策略接口，添加图表特有的方法
 */
export interface IChartDataLoadingStrategy extends IBaseStrategy<ChartConfig, ChartDataItem[]> {
  /** 支持的图表类型 */
  readonly supportedChartTypes: string[];

  /** 切换数据源 */
  switchDataSource(chartConfig: ChartConfig, newDataSource: string): Promise<ChartDataItem[]>;

  /** 获取数据源切换信息（可选，不支持数据源切换的图表可不实现） */
  getDataSourceSwitchInfo?(currentDataSource: string, newDataSource: string): Promise<DataSourceSwitchInfo>;

  /** 更新图表配置 - 将数据转换为图表配置更新 */
  updateChartConfig(chartConfig: ChartConfig, data: ChartDataItem[], newDataSource?: string): Promise<Partial<ChartConfig>>;
}

// ========== 图表数据加载策略抽象基类 ==========

/**
 * 图表数据加载策略抽象基类
 * 继承通用策略基类，实现图表特有的逻辑
 */
export abstract class BaseChartDataLoadingStrategy extends BaseStrategy<ChartConfig, ChartDataItem[]> implements IChartDataLoadingStrategy {
  // 抽象属性 - 子类必须实现
  abstract readonly supportedChartTypes: string[];

  constructor() {
    super();
  }

  /**
   * 获取配置ID
   */
  protected getConfigId(config: ChartConfig): string {
    return config.id;
  }

  /**
   * 配置验证
   */
  validateConfig(chartConfig: ChartConfig): boolean {
    return !!(chartConfig.id && this.supportedChartTypes.includes(chartConfig.type));
  }

  /**
   * 数据验证
   */
  protected validateData(data: ChartDataItem[]): void {
    if (!Array.isArray(data)) {
      throw new Error('转换后的数据必须是数组格式');
    }
  }

  /**
   * 默认的数据源切换实现
   */
  async switchDataSource(chartConfig: ChartConfig, newDataSource: string): Promise<ChartDataItem[]> {
    console.log(`切换数据源: ${chartConfig.id} -> ${newDataSource}`);
    return this.loadData(chartConfig);
  }

  /**
   * 获取数据源切换信息（默认实现，不支持数据源切换）
   * 子类如果支持数据源切换，可以重写此方法
   */
  async getDataSourceSwitchInfo(currentDataSource: string, _newDataSource: string): Promise<DataSourceSwitchInfo> {
    // 默认不支持数据源切换，返回当前数据源信息
    return {
      alternativeDataSource: currentDataSource,
    };
  }

  /**
   * 更新图表配置（抽象方法，子类必须实现）
   */
  abstract updateChartConfig(chartConfig: ChartConfig, data: ChartDataItem[], newDataSource?: string): Promise<Partial<ChartConfig>>;

  /**
   * 获取支持的类型列表（兼容性方法）
   */
  get supportedTypes(): string[] {
    return this.supportedChartTypes;
  }
}

// ========== 兼容性导出 ==========

// 装饰器已移除，不再需要兼容性导出

// ========== 具体策略实现 ==========

/**
 * 线索来源图表数据加载策略
 */
export class ClueSourceChartStrategy extends BaseChartDataLoadingStrategy {
  readonly strategyType = 'source-of-clues';
  readonly supportedChartTypes = ['bar', 'column'];

  protected async fetchApiData(params: commonQueryParams, chartConfig?: any): Promise<any> {
    // 动态导入API方法，避免循环依赖
    const { queryAllClueSource, queryValidClueSource } = await import('../api');

    // 🔥 根据图表配置中的数据源选择合适的API
    const dataSource = chartConfig?.dataSource || chartConfig?.customProps?.currentDataSource;

    if (dataSource === 'sourceOfEffectiveClues') {
      // console.log('🎯 使用有效线索来源API');
      return await queryValidClueSource(params);
    } else {
      // console.log('🎯 使用全量线索来源API');
      return await queryAllClueSource(params);
    }
  }

  protected async transformData(apiData: any): Promise<ChartDataItem[]> {
    // 动态导入数据转换工具
    const { transformApiDataToChartData } = await import('../utils/dataTransform');
    return transformApiDataToChartData(apiData);
  }

  async switchDataSource(chartConfig: ChartConfig, newDataSource: string): Promise<ChartDataItem[]> {
    // 通过图表配置传递数据源信息
    const configWithNewDataSource = {
      ...chartConfig,
      dataSource: newDataSource,
      customProps: {
        ...chartConfig.customProps,
        currentDataSource: newDataSource,
      },
    };

    return this.loadData(configWithNewDataSource);
  }

  /**
   * 线索来源策略的数据源切换信息获取实现
   * 重写基类方法，提供更精确的线索来源相关标题
   */
  async getDataSourceSwitchInfo(_currentDataSource: string, newDataSource: string): Promise<DataSourceSwitchInfo> {
    const clueDataSourceAlternativeMap: Record<string, string> = {
      sourceOfAllClues: 'sourceOfEffectiveClues',
      sourceOfEffectiveClues: 'sourceOfAllClues',
    };

    const newTitle = ClueSourceChartConfigManager.getTitleByDataSource(newDataSource);

    const alternativeDataSource = clueDataSourceAlternativeMap[newDataSource];
    const alternativeTitle = ClueSourceChartConfigManager.getTitleByDataSource(alternativeDataSource);

    return {
      newTitle,
      alternativeTitle,
      alternativeDataSource,
    };
  }

  async updateChartConfig(chartConfig: ChartConfig, data: ChartDataItem[], newDataSource?: string): Promise<Partial<ChartConfig>> {
    // 动态导入线索来源图表配置更新方法
    const { updateClueSourceChartData } = await import('../config/clueSourceChartConfig');

    const updateConfig = updateClueSourceChartData(
      chartConfig,
      data as any,
      chartConfig.customProps?.drillDownLevel || 0,
      chartConfig.customProps?.drillDownData,
      chartConfig.customProps?.parentData,
      !data || data.length === 0
    );

    // 添加通用状态更新
    updateConfig.customProps = {
      ...updateConfig.customProps,
      loading: false,
      isEmpty: !data || data.length === 0,
      lastUpdated: new Date().toISOString(),
    };

    // 如果有新数据源，更新数据源信息
    if (newDataSource) {
      updateConfig.dataSource = newDataSource;
      updateConfig.customProps = {
        ...updateConfig.customProps,
        currentDataSource: newDataSource,
      };
    }

    return updateConfig;
  }
}

/**
 * 线索有效性图表数据加载策略
 */
export class ClueEffectivenessChartStrategy extends BaseChartDataLoadingStrategy {
  readonly strategyType = 'clue-effectiveness';
  readonly supportedChartTypes = ['line'];

  protected async fetchApiData(params: commonQueryParams): Promise<any> {
    // 动态导入API方法，避免循环依赖
    const { queryClueValidStatistics } = await import('../api');
    return await queryClueValidStatistics(params);
  }

  protected async transformData(apiData: any): Promise<ChartDataItem[]> {
    // 动态导入数据转换工具
    const { transformClueValidStatisticsToChartData } = await import('../utils/dataTransform');

    // 转换API数据为图表数据格式
    const chartData = transformClueValidStatisticsToChartData(apiData);

    // console.log('🎯 线索有效性策略 - 转换后的图表数据:', chartData);
    return chartData;
  }

  // 线索有效性图表不支持数据源切换，使用父类默认实现

  async updateChartConfig(chartConfig: ChartConfig, data: ChartDataItem[], newDataSource?: string): Promise<Partial<ChartConfig>> {
    // 动态导入线索有效性图表配置更新方法
    const { updateClueEffectivenessChartData } = await import('../config/clueEffectivenessChartConfig');

    const updateConfig = updateClueEffectivenessChartData(chartConfig, data);

    // 添加通用状态更新
    updateConfig.customProps = {
      ...updateConfig.customProps,
      loading: false,
      isEmpty: !data || data.length === 0,
      lastUpdated: new Date().toISOString(),
    };

    // 如果有新数据源，更新数据源信息
    if (newDataSource) {
      updateConfig.dataSource = newDataSource;
      updateConfig.customProps = {
        ...updateConfig.customProps,
        currentDataSource: newDataSource,
      };
    }

    return updateConfig;
  }
}

/**
 * UTM图表数据加载策略
 */
export class UtmChartStrategy extends BaseChartDataLoadingStrategy {
  readonly strategyType = 'utm-chart';
  readonly supportedChartTypes = ['bar', 'column'];

  protected async fetchApiData(params: commonQueryParams, chartConfig?: any): Promise<any> {
    // 动态导入API方法，避免循环依赖
    const { queryAllClueUtmSource, queryAllClueUtmMediumSource } = await import('../api');

    const dataSource = chartConfig?.dataSource || chartConfig?.customProps?.currentDataSource || 'utmSource';
    const validDataSource = dataSource === 'utmMedium' ? 'utmMedium' : 'utmSource';

    // 根据数据源类型调用不同的API接口
    if (validDataSource === 'utmMedium') {
      return await queryAllClueUtmMediumSource(params);
    } else {
      return await queryAllClueUtmSource(params);
    }
  }

  protected async transformData(apiData: any, chartConfig?: ChartConfig): Promise<ChartDataItem[]> {
    // 动态导入数据转换工具和配置管理器
    const { transformUtmApiDataToChartData } = await import('../utils/dataTransform');
    const { updateUtmChartData } = await import('../config/utmChartConfig');
    const { useTabConfigManager } = await import('../hooks/useTabConfigManager');

    const dataSource = chartConfig?.dataSource || chartConfig?.customProps?.currentDataSource || 'utmSource';
    const validDataSource = dataSource === 'utmMedium' ? 'utmMedium' : 'utmSource';

    // 根据数据源选择对应的数据列表
    let utmDataList: any[] = [];
    if (validDataSource === 'utmSource') {
      utmDataList = apiData?.clueUtmSourceList || [];
    } else {
      utmDataList = apiData?.clueUtmMediumList || [];
    }

    // 获取动态UTM渠道列表
    const utmList: string[] = apiData?.utmList || [];

    // 使用dataTransform转换API数据为图表数据格式
    const chartData = transformUtmApiDataToChartData(utmDataList, utmList, validDataSource);
    // console.log('🎯 UTM策略 - 转换后的图表数据:', chartData);

    // 使用UTM配置管理器更新图表配置
    if (chartConfig?.id) {
      const updatedConfigPart = updateUtmChartData(chartConfig, chartData, utmList);
      // console.log('🎯 UTM策略 - 生成的配置更新:', updatedConfigPart);

      // 更新图表配置到配置管理器
      const tabConfigManager = useTabConfigManager();
      tabConfigManager.updateChartConfig(chartConfig.id, {
        ...updatedConfigPart,
        customProps: {
          ...chartConfig.customProps,
          ...updatedConfigPart.customProps,
          currentDataSource: validDataSource,
        },
      });
    }

    // 转换为ChartDataItem格式
    const result: ChartDataItem[] = [];
    chartData.forEach((monthData) => {
      Object.entries(monthData.channels).forEach(([channelKey, value]) => {
        if (value > 0) {
          result.push({
            name: `${monthData.name}-${channelKey}`,
            value: value,
            extra: {
              month: monthData.name,
              channel: channelKey,
              dataSource: validDataSource,
            },
          });
        }
      });
    });
    // console.log('🎯 UTM策略 - 最终返回结果:', result);

    return result;
  }

  async switchDataSource(chartConfig: ChartConfig, newDataSource: string): Promise<ChartDataItem[]> {
    // 通过图表配置传递数据源信息
    const configWithNewDataSource = {
      ...chartConfig,
      dataSource: newDataSource,
      customProps: {
        ...chartConfig.customProps,
        currentDataSource: newDataSource,
      },
    };

    return this.loadData(configWithNewDataSource);
  }

  /**
   * UTM图表策略的数据源切换信息获取实现
   */
  async getDataSourceSwitchInfo(_currentDataSource: string, newDataSource: string): Promise<DataSourceSwitchInfo> {
    // 动态导入UTM配置管理器
    const { UtmChartConfigManager } = await import('../config/utmChartConfig');

    // 使用配置管理器获取新数据源的标题
    const newTitle = UtmChartConfigManager.getTitleByDataSource(newDataSource);

    // 使用配置管理器获取替代信息
    const alternativeInfo = UtmChartConfigManager.getAlternativeInfo(newDataSource);

    return {
      newTitle,
      alternativeTitle: alternativeInfo.alternativeTitle,
      alternativeDataSource: alternativeInfo.alternative,
    };
  }

  async updateChartConfig(chartConfig: ChartConfig, data: ChartDataItem[], newDataSource?: string): Promise<Partial<ChartConfig>> {
    // UTM图表的配置更新逻辑已经在transformData中处理
    // 这里只需要添加通用的状态更新
    const updateConfig: Partial<ChartConfig> = {
      customProps: {
        ...chartConfig.customProps,
        loading: false,
        isEmpty: !data || data.length === 0,
        lastUpdated: new Date().toISOString(),
      },
    };

    // 如果有新数据源，更新数据源信息
    if (newDataSource) {
      updateConfig.dataSource = newDataSource;
      updateConfig.customProps = {
        ...updateConfig.customProps,
        currentDataSource: newDataSource,
      };
    }

    return updateConfig;
  }
}

/**
 * 线索跟进状态图表策略
 * 负责线索跟进状态相关图表的数据加载和处理
 */
export class FollowUpStatusChartStrategy extends BaseChartDataLoadingStrategy {
  readonly strategyType = 'follow-up-status-chart';
  readonly supportedChartTypes = ['bar', 'column'];

  protected async fetchApiData(params: commonQueryParams): Promise<ClueValidStatusResponse> {
    const response = await queryClueValidStatus(params);
    return response;
  }

  protected async transformData(apiData: ClueValidStatusResponse, chartConfig?: ChartConfig): Promise<ChartDataItem[]> {
    // 动态导入数据转换工具和配置管理器
    const { transformClueValidStatusToChartData } = await import('../utils/dataTransform');
    const { useTabConfigManager } = await import('../hooks/useTabConfigManager');

    // 获取API返回的有效状态列表，如果没有则使用所有枚举值
    const validStatusList =
      apiData.validStatusList && apiData.validStatusList.length > 0 ? apiData.validStatusList : (Object.values(ClueValidStatusEnum) as number[]);

    // 使用dataTransform转换API数据为图表数据格式
    const chartData = transformClueValidStatusToChartData(apiData, validStatusList as number[]);

    // 使用配置管理器更新图表配置
    if (chartConfig?.id) {
      const followUpStatusChartConfigManager = FollowUpStatusChartConfigManager.getInstance();
      const updatedConfigPart = followUpStatusChartConfigManager.updateChartData(chartConfig, chartData, validStatusList);

      // 更新图表配置到配置管理器
      const tabConfigManager = useTabConfigManager();
      tabConfigManager.updateChartConfig(chartConfig.id, {
        ...updatedConfigPart,
        customProps: {
          ...chartConfig.customProps,
          ...updatedConfigPart.customProps,
          validStatusList,
        },
      });
    }

    // 转换为ChartDataItem格式
    const result: ChartDataItem[] = [];
    chartData.forEach((monthData) => {
      Object.entries(monthData.channels).forEach(([channelKey, value]) => {
        if (value > 0) {
          result.push({
            name: `${monthData.name}-${channelKey}`,
            value: value,
            extra: {
              month: monthData.name,
              channel: channelKey,
              statusId: channelKey,
            },
          });
        }
      });
    });

    return result;
  }

  async updateChartConfig(chartConfig: ChartConfig, data: ChartDataItem[], newDataSource?: string): Promise<Partial<ChartConfig>> {
    // 线索跟进状态图表的配置更新逻辑已经在transformData中处理
    // 这里只需要添加通用的状态更新
    const updateConfig: Partial<ChartConfig> = {
      customProps: {
        ...chartConfig.customProps,
        loading: false,
        isEmpty: !data || data.length === 0,
        lastUpdated: new Date().toISOString(),
      },
    };

    // 如果有新数据源，更新数据源信息
    if (newDataSource) {
      updateConfig.dataSource = newDataSource;
      updateConfig.customProps = {
        ...updateConfig.customProps,
        currentDataSource: newDataSource,
      };
    }

    return updateConfig;
  }
}

/**
 * 线索跟进时间图表数据加载策略
 * 负责线索首次跟进时长分析图表的数据加载和处理
 */
export class ClueFollowUpTimeStrategy extends BaseChartDataLoadingStrategy {
  readonly strategyType = 'clue-follow-up-time';
  readonly supportedChartTypes = ['bar', 'column'];

  /**
   * 获取API数据
   */
  protected async fetchApiData(params: commonQueryParams): Promise<ClueValidFollowTimeResponse> {
    const response = await queryClueValidFirstFollowTime(params);
    return response;
  }

  /**
   * 转换API数据为图表数据格式
   */
  protected async transformData(apiData: ClueValidFollowTimeResponse, chartConfig?: ChartConfig): Promise<ChartDataItem[]> {
    // 动态导入数据转换工具
    const { transformFirstFollowUpTimeToChartData } = await import('../utils/dataTransform');
    const chartData = transformFirstFollowUpTimeToChartData(apiData);

    // 使用配置管理器更新图表配置
    if (chartConfig?.id) {
      const { updateFirstFollowUpTimeAnalysisChartData } = await import('../config/FirstFollowUpTimeAnalysisChartConfig');
      const updatedConfigPart = updateFirstFollowUpTimeAnalysisChartData(chartConfig, chartData);

      // 更新图表配置到配置管理器
      const { useTabConfigManager } = await import('../hooks/useTabConfigManager');
      const tabConfigManager = useTabConfigManager();
      tabConfigManager.updateChartConfig(chartConfig.id, {
        ...updatedConfigPart,
        customProps: {
          ...chartConfig.customProps,
          ...updatedConfigPart.customProps,
        },
      });
    }

    return chartData;
  }

  /**
   * 更新图表配置
   */
  async updateChartConfig(chartConfig: ChartConfig, data: ChartDataItem[], newDataSource?: string): Promise<Partial<ChartConfig>> {
    // 线索跟进时间图表的配置更新逻辑已经在transformData中处理
    // 这里只需要添加通用的状态更新
    const updateConfig: Partial<ChartConfig> = {
      customProps: {
        ...chartConfig.customProps,
        loading: false,
        isEmpty: !data || data.length === 0,
        lastUpdated: new Date().toISOString(),
      },
    };

    // 如果有新数据源，更新数据源信息
    if (newDataSource) {
      updateConfig.dataSource = newDataSource;
      updateConfig.customProps = {
        ...updateConfig.customProps,
        currentDataSource: newDataSource,
      };
    }

    return updateConfig;
  }
}

/**
 * 首次响应超时图表策略
 */
export class FirstResponseTimeoutChartStrategy extends BaseChartDataLoadingStrategy {
  readonly strategyType = 'firstResponseTimeout';
  readonly supportedChartTypes = ['line'];

  protected async fetchApiData(params: commonQueryParams): Promise<ClueAllFirstRespOverTimeResponse> {
    const response = await queryClueAllFirstRespOverTime(params);
    return response;
  }

  protected async transformData(apiData: ClueAllFirstRespOverTimeResponse, chartConfig?: ChartConfig): Promise<ChartDataItem[]> {
    // 使用 dataTransform.ts 中的转换函数
    const { transformFirstResponseTimeoutToChartData } = await import('../utils/dataTransform');
    const chartData = transformFirstResponseTimeoutToChartData(apiData);

    // 使用配置管理器更新图表配置
    if (chartConfig?.id) {
      const firstResponseTimeoutChartConfigManager = FirstResponseTimeoutChartConfigManager.getInstance();
      const updatedConfigPart = firstResponseTimeoutChartConfigManager.updateChartData(chartConfig, chartData);

      // 更新图表配置到配置管理器
      const { useTabConfigManager } = await import('../hooks/useTabConfigManager');
      const tabConfigManager = useTabConfigManager();
      tabConfigManager.updateChartConfig(chartConfig.id, {
        ...updatedConfigPart,
        customProps: {
          ...chartConfig.customProps,
          ...updatedConfigPart.customProps,
        },
      });
    }

    return chartData;
  }

  async updateChartConfig(chartConfig: ChartConfig, data: ChartDataItem[]): Promise<Partial<ChartConfig>> {
    const configManager = FirstResponseTimeoutChartConfigManager.getInstance();
    const updatedConfig = configManager.updateChartData(chartConfig, data);

    return {
      ...updatedConfig,
      customProps: {
        ...chartConfig.customProps,
        ...updatedConfig.customProps,
        isEmpty: data.length === 0,
        lastUpdated: new Date().toISOString(),
      },
    };
  }
}

/**
 * 线索转化分析图表数据加载策略
 * 负责加载和处理线索转化分析数据，生成漏斗图配置
 */
export class LeadConversionChartStrategy extends BaseChartDataLoadingStrategy {
  readonly strategyType = 'lead-conversion-analysis';
  readonly supportedChartTypes = ['funnel'];

  protected async fetchApiData(params: commonQueryParams): Promise<ClueConversionDataAnalyzeByStatusResponse> {
    return await queryClueConversionDataAnalyzeByStatus(params);
  }

  protected async transformData(apiData: ClueConversionDataAnalyzeByStatusResponse): Promise<ChartDataItem[]> {
    // 使用专门的数据转换函数
    const leadConversionData = transformClueConversionDataAnalyzeByStatusToChartData(apiData);

    // 转换为通用的ChartDataItem格式
    const chartData: ChartDataItem[] = leadConversionData.map((stage, index) => ({
      date: `stage-${index}`, // 漏斗图不需要日期，使用阶段索引
      value: stage.value,
      name: stage.name,
      color: stage.color,
      percentage: stage.conversionRate,
      customData: {
        conversionRate: stage.conversionRate,
        stageName: stage.name,
        stageValue: stage.value,
      },
    }));

    return chartData;
  }

  async updateChartConfig(chartConfig: ChartConfig, data: ChartDataItem[]): Promise<Partial<ChartConfig>> {
    const configManager = LeadConversionChartConfigManager.getInstance();

    // 将ChartDataItem转换回LeadConversionStageConfig格式
    const leadConversionData = data.map((item) => ({
      name: item.name,
      value: item.value,
      conversionRate: item.customData?.conversionRate || 0,
      color: item.color,
    }));

    const updatedConfig = configManager.updateChartData(chartConfig, leadConversionData);

    return updatedConfig;
  }
}

/**
 * 车型成交分析图表数据加载策略
 */
export class VehicleModelDealChartStrategy extends BaseChartDataLoadingStrategy {
  readonly strategyType = 'vehicle-model-deal-analysis';
  readonly supportedChartTypes = ['bar', 'column'];

  protected async fetchApiData(params: commonQueryParams): Promise<ClueConversionDataAnalyzeByModelResponse> {
    // 动态导入API方法，避免循环依赖
    const { queryClueConversionDataAnalyzeByModel } = await import('../api');
    return await queryClueConversionDataAnalyzeByModel(params);
  }

  protected async transformData(apiData: ClueConversionDataAnalyzeByModelResponse): Promise<ChartDataItem[]> {
    // 动态导入数据转换工具
    const { transformClueConversionDataAnalyzeByModelToChartData } = await import('../utils/dataTransform');

    // 转换API数据为图表数据格式
    const chartData = transformClueConversionDataAnalyzeByModelToChartData(apiData);

    // console.log('🎯 车型成交分析策略 - 转换后的图表数据:', chartData);
    return chartData;
  }

  // 车型成交分析图表不支持数据源切换，使用父类默认实现

  async updateChartConfig(chartConfig: ChartConfig, data: ChartDataItem[]): Promise<Partial<ChartConfig>> {
    // 动态导入车型成交分析图表配置更新方法
    const { updateVehicleModelDealChartData } = await import('../config/vehicleModelDealChartConfig');

    let averageDealCycle = 0;
    if (data.length > 0) {
      averageDealCycle = data[0].extra.averageCycles;
    }
    const updateConfig = updateVehicleModelDealChartData(chartConfig, data, averageDealCycle);

    // 添加通用状态更新
    updateConfig.customProps = {
      ...updateConfig.customProps,
      loading: false,
      isEmpty: !data || data.length === 0,
      lastUpdated: new Date().toISOString(),
      averageDealCycle, // 保存平均成交周期供后续使用
    };

    return updateConfig;
  }
}

/**
 * 用户分析图表数据加载策略
 * 负责用户分析图表的数据加载和处理，包含用户新增和用户注销两条趋势线
 */
export class UserAnalysisChartStrategy extends BaseChartDataLoadingStrategy {
  readonly strategyType = 'user-analysis-chart';
  readonly supportedChartTypes = ['bar', 'column', 'line'];

  protected async fetchApiData(params: commonQueryParams): Promise<UserAnalysisChartDataResponse> {
    // 动态导入API方法，避免循环依赖
    const { queryUserAnalysisData } = await import('../api');
    return await queryUserAnalysisData(params);
  }

  protected async transformData(apiData: UserAnalysisChartDataResponse): Promise<ChartDataItem[]> {
    // 使用数据转换工具转换API数据，结构化数据已嵌入到ChartDataItem中
    return transformUserAnalysisChartDataToChartData(apiData);
  }

  async updateChartConfig(chartConfig: ChartConfig, data: ChartDataItem[]): Promise<Partial<ChartConfig>> {
    // 动态导入用户分析图表配置更新方法
    const { updateUserAnalysisChartData } = await import('../config/userAnalysisChartConfig');

    // 直接使用传入的数据，结构化信息已嵌入其中
    const updateConfig = updateUserAnalysisChartData(chartConfig, data);

    // 添加通用状态更新
    updateConfig.customProps = {
      ...updateConfig.customProps,
      loading: false,
      isEmpty: !data || data.length === 0,
      lastUpdated: new Date().toISOString(),
    };

    return updateConfig;
  }
}

// ========== 工厂模式：策略创建和管理 ==========

/**
 * 图表数据加载策略工厂
 * 🔥 优化：继承 BaseSingletonStrategyFactory，使用统一的单例策略管理
 */
export class ChartDataLoadingStrategyFactory extends BaseSingletonStrategyFactory<IChartDataLoadingStrategy, ChartConfig> {
  private constructor() {
    super('ChartDataLoadingStrategyFactory');
    console.log('🏭 ChartDataLoadingStrategyFactory 实例已创建');
  }

  /**
   * 获取工厂单例实例
   */
  public static getInstance(): ChartDataLoadingStrategyFactory {
    return this.getOrCreateInstance('ChartDataLoadingStrategyFactory', () => new ChartDataLoadingStrategyFactory());
  }

  /**
   * 获取策略实例 - 实现基类的抽象方法
   */
  getStrategy(chartConfig: ChartConfig): IChartDataLoadingStrategy {
    const strategy = this.getStrategyByType(chartConfig.loadingStrategy);
    if (!strategy) {
      throw new Error('未找到指定的图表数据加载策略');
    }
    return strategy;
  }

  /**
   * 静态方法：注册策略
   */
  static registerStrategy(strategy: IChartDataLoadingStrategy): void {
    ChartDataLoadingStrategyFactory.getInstance().registerStrategy(strategy);
  }

  /**
   * 静态方法：根据图表配置创建合适的策略
   */
  static createStrategy(chartConfig: ChartConfig): IChartDataLoadingStrategy {
    return ChartDataLoadingStrategyFactory.getInstance().getStrategy(chartConfig);
  }

  /**
   * 静态方法：获取所有注册的策略
   */
  static getAllStrategies(): IChartDataLoadingStrategy[] {
    return ChartDataLoadingStrategyFactory.getInstance().getAllStrategies();
  }

  /**
   * 静态方法：清除所有策略（用于测试）
   */
  static clearStrategies(): void {
    ChartDataLoadingStrategyFactory.getInstance().clearStrategies();
  }

  /**
   * 静态方法：初始化默认策略
   */
  static initializeDefaultStrategies(): void {
    // 🔥 优化：使用依赖注入创建策略实例
    this.registerStrategy(createChartDataLoadingStrategy(ClueSourceChartStrategy));
    this.registerStrategy(createChartDataLoadingStrategy(ClueEffectivenessChartStrategy));
    this.registerStrategy(createChartDataLoadingStrategy(UtmChartStrategy));
    this.registerStrategy(createChartDataLoadingStrategy(FollowUpStatusChartStrategy));
    this.registerStrategy(createChartDataLoadingStrategy(ClueFollowUpTimeStrategy));
    this.registerStrategy(createChartDataLoadingStrategy(FirstResponseTimeoutChartStrategy));
    this.registerStrategy(createChartDataLoadingStrategy(LeadConversionChartStrategy));
    this.registerStrategy(createChartDataLoadingStrategy(VehicleModelDealChartStrategy));
    this.registerStrategy(createChartDataLoadingStrategy(UserAnalysisChartStrategy));
    console.log('🚀 默认策略初始化完成（使用依赖注入）');
  }
}

export const chartDataLoadingStrategyFactory = ChartDataLoadingStrategyFactory.getInstance();

// 自动初始化默认策略
ChartDataLoadingStrategyFactory.initializeDefaultStrategies();
