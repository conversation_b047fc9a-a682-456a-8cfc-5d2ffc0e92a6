<template>
  <div v-for="(group, inx) in data" :key="inx">
    <!-- 标题 -->
    <h3 class="group_title">{{ group.title }}</h3>

    <!-- 🔥 局部筛选区层 -->
    <div v-if="group.localFilterConfig?.enabled" class="local-filter-container">
      <LocalFilterPanel :config="group.localFilterConfig" :group-id="group.id" />
    </div>

    <!-- 🔥 新增：使用独立的统计数据组件 -->
    <StatisticsContainer v-if="group.statisticsConfig" :statistics-config="group.statisticsConfig" :group-title="group.title" />

    <!-- 图表数据层 -->
    <div class="chart-grid" :style="gridStyle" v-if="group.chartList.length">
      <div v-for="chart in group.chartList" :key="chart.id">
        <div class="chart-item" :data-chart-id="chart.id">
          <ChartGroup :chart-id="chart.id" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
  export default {
    name: 'ChartGroupContainer',
  };
</script>
<script setup lang="ts">
  import { onMounted } from 'vue';
  import type { TabConfigGroupConfig } from '../types/statisticDashboard';
  import ChartGroup from './ChartGroup.vue';
  import StatisticsContainer from './StatisticsContainer.vue';
  import LocalFilterPanel from './LocalFilterPanel.vue';
  import { useInitializationContext } from '../hooks/useInitializationContext';

  interface Props {
    gridStyle: any;
    data: TabConfigGroupConfig[];
  }

  const props = defineProps<Props>();

  // 使用初始化上下文
  const initContext = useInitializationContext();

  /**
   * 等待所有局部筛选器初始化完成
   * 现在通过初始化上下文管理，无需暴露给父组件
   */
  const waitForAllLocalFiltersInitialized = async () => {
    const localFilterComponentIds: string[] = [];

    // 收集所有启用了局部筛选器的组件ID
    for (const group of props.data) {
      if (group.localFilterConfig?.enabled) {
        localFilterComponentIds.push(`local-filter-panel-${group.id}`);
      }
    }

    // 通过初始化上下文等待所有局部筛选器就绪
    if (localFilterComponentIds.length > 0) {
      await initContext.waitForComponents(localFilterComponentIds);
      console.log('🎯 所有局部筛选器初始化完成:', localFilterComponentIds);
    }
  };

  // 组件挂载时自动等待局部筛选器初始化
  onMounted(async () => {
    try {
      await waitForAllLocalFiltersInitialized();
      console.log('🎯 ChartGroupContainer 局部筛选器初始化完成');
    } catch (error) {
      console.error('🚨 ChartGroupContainer 局部筛选器初始化失败:', error);
    }
  });
</script>
<style lang="less" scoped>
  .group_title {
    margin-left: 30px;
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
  }

  .local-filter-container {
    margin: 0 20px 20px 20px;
    padding: 16px;
    padding-bottom: 0;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.04);
  }

  .chart-grid {
    margin-bottom: 32px;
  }

  .chart-item {
    height: 100%;
  }
</style>
