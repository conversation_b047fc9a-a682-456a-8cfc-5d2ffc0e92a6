<template>
  <div class="statistics-container relative">
    <!-- 🔥 整体loading遮罩 -->
    <div v-if="loading" class="absolute inset-0 bg-white/30 backdrop-blur-sm flex items-center justify-center z-10 rounded-lg">
      <div class="bg-white rounded-lg p-4 shadow-lg">
        <a-spin size="large" />
      </div>
    </div>

    <!-- 统计数据网格布局 -->
    <div class="statistics-grid" :class="{ 'opacity-30': loading }">
      <div v-for="item in statisticsItems" :key="item.id" class="statistics-item">
        <!-- 标题区域 -->
        <div class="item-header">
          <p class="item-title">{{ item.title }}</p>
          <a-tag v-if="item.core" color="blue" class="core-indicator">
            {{ t('coreIndicators') }}
          </a-tag>
        </div>

        <!-- 主要数值 总量或百分比 -->
        <div class="item-value">
          <h3>{{ item.total }}</h3>
        </div>

        <!-- 趋势指标 -->
        <div class="item-trends">
          <!-- 环比 - 对应chainComparison字段（yearRatio数据），可能不存在 -->
          <div v-if="item.chainComparison !== undefined" class="trend-item">
            <span class="trend-label">{{ t('chain') }}:</span>
            <a-tag class="trend-tag" :color="item.chainTrend ? 'green' : 'red'">
              <Icon :icon="item.chainTrend ? 'ant-design:arrow-up-outlined' : 'ant-design:arrow-down-outlined'" />
              {{ `${item.chainComparison}%` }}
            </a-tag>
          </div>

          <!-- 🔥 新增：当没有环比数据时显示提示 -->
          <div v-else class="trend-item">
            <span class="trend-label">{{ t('chain') }}:</span>
            <a-tag class="trend-tag no-data">
              {{ t('emptyData') }}
            </a-tag>
          </div>

          <!-- 同比 - 对应yearComparison字段（ringRatio数据），可能不存在 -->
          <div v-if="item.yearComparison !== undefined" class="trend-item">
            <span class="trend-label">{{ t('onYear') }}:</span>
            <a-tag class="trend-tag" :color="item.yearTrend ? 'green' : 'red'">
              <Icon :icon="item.yearTrend ? 'ant-design:arrow-up-outlined' : 'ant-design:arrow-down-outlined'" />
              {{ `${item.yearComparison}%` }}
            </a-tag>
          </div>

          <!-- 🔥 当没有同比数据时显示提示 -->
          <div v-else class="trend-item">
            <span class="trend-label">{{ t('onYear') }}:</span>
            <a-tag class="trend-tag no-data">
              {{ t('emptyData') }}
            </a-tag>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
  export default {
    name: 'StatisticsContainer',
  };
</script>

<script setup lang="ts">
  import { computed } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useStatisticsDataOptional, useStatisticsActionsOptional } from '../hooks/useStatisticsActions';
  import type { StatisticsConfig } from '../types/statisticDashboard';

  const { t } = useI18n('common');

  // 🔥 使用上下文系统
  const statisticsData = useStatisticsDataOptional();
  const statisticsActions = useStatisticsActionsOptional();

  interface Props {
    /** 统计配置 */
    statisticsConfig: StatisticsConfig;
    /** 组标题 */
    groupTitle?: string;
  }

  const props = defineProps<Props>();

  // 🔥 获取统计数据项
  const statisticsItems = computed(() => {
    if (!props.statisticsConfig) return [];

    // 优先使用上下文中的数据
    const contextData = statisticsData.getStatisticsData(props.statisticsConfig.id);
    if (contextData && contextData.length > 0) {
      return contextData;
    }

    // 回退到配置中的默认数据
    return props.statisticsConfig.items || [];
  });

  // 🔥 整体loading状态
  const loading = computed(() => {
    return statisticsActions.statisticsLoadingStates[props.statisticsConfig.id] || false;
  });
</script>

<style lang="less" scoped>
  .statistics-container {
    margin-bottom: 24px;
  }

  .statistics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 48px;
    padding: 20px;
    transition: opacity 0.2s ease-in-out;
  }

  .statistics-item {
    background: #fff;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
      transform: translateY(-2px);
    }
  }

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;

    .item-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin: 0;
      flex: 1;
    }

    .core-indicator {
      border-radius: 12px;
      font-size: 11px;
      font-weight: 500;
    }
  }

  .item-value {
    margin-bottom: 16px;

    h3 {
      font-size: 28px;
      font-weight: 700;
      color: #1a1a1a;
      margin: 0;
      line-height: 1.2;
    }
  }

  .item-trends {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .trend-item {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .trend-label {
      font-size: 13px;
      color: #666;
      font-weight: 500;
    }

    .trend-tag {
      border-radius: 12px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      min-width: 60px;
      justify-content: center;

      // 🔥 新增：无数据状态样式
      &.no-data {
        background-color: #f5f5f5;
        border-color: #d9d9d9;
        color: #999;
      }
    }
  }

  // 响应式调整
  @media (max-width: 1200px) {
    .statistics-grid {
      grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
      gap: 16px;
      padding: 16px;
    }
  }

  @media (max-width: 768px) {
    .statistics-grid {
      grid-template-columns: 1fr;
      gap: 12px;
      padding: 12px;
    }

    .statistics-item {
      padding: 16px;
    }

    .item-value h3 {
      font-size: 24px;
    }
  }
</style>
