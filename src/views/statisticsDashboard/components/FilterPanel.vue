<template>
  <a-card :bordered="false" size="small">
    <div class="filter-content">
      <BasicForm @register="registerForm" @field-value-change="handleFieldChange" @submit="handleSubmit" @reset="handleReset" />
    </div>
  </a-card>
</template>

<script lang="ts">
  export default {
    name: 'FilterPanel',
  };
</script>
<script setup lang="ts">
  import { nextTick, ref, onMounted } from 'vue';
  import { useFilters } from '../hooks/useFilters';
  import { message } from 'ant-design-vue';
  import { BasicForm } from '/@/components/Form';
  import { useUnifiedQueryParamsOptional } from '../hooks/useUnifiedQueryParams';
  import { useComponentInitialization } from '../hooks/useInitializationContext';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { validateDateRange, transformValuesToQueryParams, REGION_PARAM_FIELDS } from '../utils/filterUtils';

  // Props定义
  interface Props {
    /** 是否自动应用筛选 */
    autoApply?: boolean;
    /** 是否启用用户数据权限, 开启后 会把筛选条件里的 大区 和 国家进行赋值当前登陆的用户，不允许修改 */
    enableUserDataAuth?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    autoApply: false,
    enableUserDataAuth: true,
  });

  // 移除emit定义，改为使用统一查询参数管理器的参数变化监听机制

  // 使用统一查询参数管理器
  const unifiedQueryParams = useUnifiedQueryParamsOptional();

  // 使用组件初始化管理
  const componentInit = useComponentInitialization('filter-panel');

  // 使用筛选器Hook
  const {
    registerForm,
    formMethods,
    getPeriodRangeDefaultValue,
    handleFieldChange,
    getDefaultQueryParams,
    initializationPromise,
    adjustDateRangeByPeriod,
  } = useFilters({
    ...props,
  });

  const { setFieldsValue, getFieldsValue } = formMethods;

  // 核心设计：维护独立的查询参数状态（与表单数据分离）
  const currentQueryParams = ref<Record<string, any>>(getDefaultQueryParams());

  /**
   * 初始化默认查询参数
   */
  const initializeQueryParams = () => {
    const defaultParams = getDefaultQueryParams();
    currentQueryParams.value = defaultParams;
    // 更新统一查询参数管理器的全局参数
    unifiedQueryParams?.setGlobalParams(defaultParams);
    return defaultParams;
  };

  /**
   * 等待权限数据加载完成后更新查询参数
   */
  const initializeWithAuthData = async () => {
    try {
      // 等待用户权限数据加载完成
      await initializationPromise;

      // 权限数据加载完成后，获取最新的表单值和查询参数
      const formValues = formMethods.getFieldsValue();
      if (formValues && Object.keys(formValues).length > 0) {
        // 基于最新表单值生成查询参数
        const updatedParams = transformValuesToQueryParams(formValues, adjustDateRangeByPeriod);
        currentQueryParams.value = updatedParams;

        // 更新统一查询参数管理器
        unifiedQueryParams?.updateGlobalParams(updatedParams);
      } else {
        // 如果表单值为空，使用默认参数
        initializeQueryParams();
      }
    } catch (error) {
      console.error('权限数据加载失败，使用默认查询参数:', error);
      initializeQueryParams();
    }
  };

  const { t } = useI18n('common');

  // 处理表单提交（应用筛选）
  const handleSubmit = async (values: Record<string, any>) => {
    if (Reflect.ownKeys(values).length === 0) {
      message.error(t('pleaseSelectFilteringCriteria'));
      return;
    }

    // 🔧 新增：日期范围校验
    const validation = validateDateRange(values);
    if (!validation.valid) {
      message.error(validation.message);
      return;
    }

    // 核心逻辑：将用户提交的表单值转换为查询参数
    const newQueryParams = transformValuesToQueryParams(values);

    // 更新统一查询参数管理器的全局参数
    unifiedQueryParams?.setGlobalParams(newQueryParams);

    // 同步表单状态（保持表单数据一致性）
    setFieldsValue(values);

    // 等待状态更新完成
    await nextTick();

    // 参数变化会通过统一查询参数管理器的监听机制自动通知相关组件
    console.log('🎯 筛选条件已应用，参数变化将自动触发数据刷新');
  };

  // 处理表单重置（清除筛选）
  const handleReset = async () => {
    // 重置为默认值
    const defaultRange = getPeriodRangeDefaultValue('day');

    // 清除表单值
    setFieldsValue({
      statPeriod: 'day',
      periodRange: defaultRange,
      regionCode: undefined,
      countryCode: undefined,
      provinceCode: undefined,
      cityCode: undefined,
      dealerCode: undefined,
    });

    REGION_PARAM_FIELDS.forEach((field) => {
      formMethods?.updateSchema({
        field,
        componentProps: {
          options: [],
        },
      });
    });

    // 重置
    formMethods?.updateSchema({
      field: 'networkCode',
      componentProps: {
        options: [],
      },
    });

    // 等待表单值设置完成
    await nextTick();

    // 获取重置后的表单值
    const newParams = getFieldsValue();

    // 转换为查询参数格式
    const resetQueryParams = transformValuesToQueryParams(newParams);

    // 更新内部查询参数状态
    currentQueryParams.value = resetQueryParams;

    // 更新统一查询参数管理器
    unifiedQueryParams?.setGlobalParams(resetQueryParams);

    // 参数变化会通过统一查询参数管理器的监听机制自动通知相关组件
    console.log('🎯 筛选条件已清除，参数变化将自动触发数据刷新');
  };

  // 组件挂载时初始化
  onMounted(async () => {
    componentInit.setLoading();

    try {
      // 初始化权限数据（内部会处理查询参数设置）
      await initializeWithAuthData();

      componentInit.setReady();
      console.log('🎯 FilterPanel 初始化完成');
    } catch (error) {
      console.error('🚨 FilterPanel 初始化失败:', error);
      componentInit.setError();
    }
  });
</script>
<style lang="less" scoped>
  // 调整表单组件的样式
  :deep(.ant-form-item) {
    margin-bottom: 16px !important;
  }
  :deep(.ant-row.mt-5.flex-self-start.css-dev-only-do-not-override-zww5ly) {
    margin-top: 5px !important;
  }
</style>
