<template>
  <div>
    <a-card :bordered="false" :body-style="{ padding: 0 }" class="bg-white rounded-lg shadow-sm overflow-hidden">
      <a-tabs v-model:activeKey="activeTab" type="card" size="large" @change="handleTabChange" :animated="{ inkBar: true, tabPane: false }">
        <a-tab-pane v-for="tab in tabs" :key="tab.id" :tab="tab.name">
          <!-- Tab内容区域 - 🔧 修复：使用相对定位+遮罩方式，避免DOM结构切换 -->
          <div class="relative min-h-100" :class="getLayoutClass(tab.layout)">
            <!-- 🔧 核心修复：始终渲染实际内容，避免DOM结构切换 -->
            <div class="transition-opacity duration-200 ease-in-out" :class="{ 'opacity-30 pointer-events-none': loading }">
              <div v-if="!tab.groups || tab.groups.length === 0" class="flex justify-center items-center h-100">
                <a-empty :description="t('common.emptyData')">
                  <template #image>
                    <Icon icon="ant-design:bar-chart-outlined" style="font-size: 64px; color: #d9d9d9" />
                  </template>
                </a-empty>
              </div>
              <div v-else>
                <ChartGroupContainer :data="tab.groups" :gridStyle="gridStyle" />
              </div>
            </div>

            <!-- 🔧 修复：Loading遮罩层 - 不影响DOM结构 -->
            <div v-if="loading" class="absolute inset-0 bg-white/30 backdrop-blur-sm flex items-center justify-center z-10">
              <div class="bg-white rounded-lg p-5 shadow-lg">
                <a-spin size="large" />
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script lang="ts">
  export default {
    name: 'TabContainer',
  };
</script>
<script setup lang="ts">
  import { computed, watch, reactive } from 'vue';
  import { Icon } from '/@/components/Icon';
  import type { TabConfig } from '../types/statisticDashboard';
  import ChartGroupContainer from './ChartGroupContainer.vue';
  // import { useChartConfigOptional } from '../hooks/useChartActions';
  import { useTabConfigContextOptional } from '../hooks/useTabConfigManager';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();

  // Props定义
  interface Props {
    /** Tab配置数组 */
    tabs: TabConfig[];
    /** 当前激活的Tab */
    modelValue: string;
  }

  const props = withDefaults(defineProps<Props>(), {});

  // Emits定义 - 只保留必要的v-model事件和tab切换事件
  interface Emits {
    'update:modelValue': [value: string];
    'tab-change': [tabId: string, tab: TabConfig];
  }

  const emit = defineEmits<Emits>();

  // 使用上下文系统
  // const chartConfig = useChartConfigOptional();

  // 🔥 修复：使用Tab上下文而不是直接调用hook
  const tabConfigManager = useTabConfigContextOptional();

  // 🔥 Tab的loading状态：通过Tab管理器获取当前Tab的loading状态
  const loading = computed(() => {
    return tabConfigManager?.getCurrentTabLoading() || false;
  });

  // 🔥 内联布局配置，简化hook依赖
  const layoutConfig = reactive({
    columns: 3, // 参考列数（不再固定使用）
    gap: 24, // 间距
    minChartWidth: 400, // 最小图表宽度
    maxChartWidth: 600, // 单列时的最大图表宽度
    singleColumnMaxWidth: 650, // 单列布局时的最大宽度
  });

  const getChartContainerStyle = computed(() => ({
    display: 'grid',
    gridTemplateColumns: `repeat(auto-fit, minmax(${layoutConfig.minChartWidth}px, 1fr))`,
    gap: `${layoutConfig.gap}px`,
    padding: `${layoutConfig.gap}px`,
    maxWidth: '100%',
  }));

  // 计算属性：当前激活Tab
  const activeTab = computed({
    get: () => props.modelValue,
    set: (value: string) => emit('update:modelValue', value),
  });

  // 计算属性：当前Tab配置
  const currentTab = computed(() => {
    return props.tabs.find((tab) => tab.id === activeTab.value);
  });

  // 计算属性：网格样式
  const gridStyle = computed(() => {
    const baseStyle = getChartContainerStyle.value;
    const layout = currentTab.value?.layout || 'grid';

    switch (layout) {
      case 'flex':
        return {
          display: 'flex',
          flexWrap: 'wrap' as const,
          gap: baseStyle.gap,
          padding: baseStyle.padding,
        };
      case 'custom':
        return {
          display: 'block',
          padding: baseStyle.padding,
        };
      default: // grid
        return baseStyle;
    }
  });

  /**
   * 获取布局样式类
   */
  const getLayoutClass = (layout: string) => {
    switch (layout) {
      case 'flex':
        return 'flex flex-wrap gap-4 p-4';
      case 'custom':
        return 'p-4';
      default: // grid
        return 'grid gap-4 p-4';
    }
  };

  /**
   * 处理Tab切换 - 统一使用上下文系统
   */
  const handleTabChange = (tabId: string) => {
    const tab = props.tabs.find((t) => t.id === tabId);
    if (tab) {
      // 使用Tab配置上下文设置当前Tab
      tabConfigManager?.setActiveTab(tabId);
      emit('tab-change', tabId, tab);
    }
  };

  // 监听Tab变化，预加载数据
  watch(
    () => activeTab.value,
    (newTabId) => {
      const tab = props.tabs.find((t) => t.id === newTabId);
      if (tab && tab.groups) {
        // 这里可以添加预加载逻辑
      }
    },
    { immediate: true }
  );
</script>
