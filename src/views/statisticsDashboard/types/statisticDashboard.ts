/**
 * 统计看板类型定义
 * 基于ECharts类型定义，实现数据可视化看板的核心类型接口
 */

// 复用 ECharts 提供的类型定义
import type { EChartsOption } from 'echarts';

/**
 * 线索来源类型枚举
 */
export enum ClueSourceType {
  /** 线上线索 */
  ONLINE = 'online',
  /** 线下线索 */
  OFFLINE = 'offline',
  /** 有效线索 */
  EFFECTIVE = 'effective',
}

/**
 * 线索来源二级类型枚举
 */
export enum ClueSourceSecondaryType {
  /** APP */
  APP = 'app',
  /** 官网 */
  WEBSITE = 'website',
  /** 客服中心 */
  SERVICE_CENTER = 'serviceCenter',
}

/**
 * 线索来源二级类型映射
 */
export const ClueSourceSecondaryTypeMap = {
  [ClueSourceSecondaryType.APP]: 'APP',
  [ClueSourceSecondaryType.WEBSITE]: '官网',
  [ClueSourceSecondaryType.SERVICE_CENTER]: '客服中心',
};

/**
 * 线索来源颜色映射
 */
export const ClueSourceColorMap = {
  [ClueSourceType.ONLINE]: '#5470c6',
  [ClueSourceType.OFFLINE]: '#91cc75',
  [ClueSourceType.EFFECTIVE]: '#fac858',
};

/**
 * 线索来源二级颜色映射
 */
export const ClueSourceSecondaryColorMap = {
  [ClueSourceSecondaryType.APP]: '#9c27b0', // 紫色
  [ClueSourceSecondaryType.WEBSITE]: '#e91e63', // 粉色
  [ClueSourceSecondaryType.SERVICE_CENTER]: '#00bcd4', // 青色
};

/**
 * 异步数据提供者接口
 */
export interface DrillDownDataProvider {
  /** 获取下探数据 */
  fetchData: (parentData: ChartDataItem, level: number, config: ChartConfig) => Promise<ChartDataItem[]>;
  /** 更新图表配置（可选） */
  updateChartConfig?: (data: ChartDataItem[], level: number, config: ChartConfig, parentData: ChartDataItem) => Partial<ChartConfig>;
}

/**
 * 数据下探层级配置
 */
export interface DrillDownLevel {
  /** 层级编号 */
  level: number;
  /** 数据键名 */
  dataKey: string;
  /** 标题字段名 */
  titleField: string;
  /** 数值字段名 */
  valueField: string;
  /** 颜色字段名（可选） */
  colorField?: string;
  /** 父级键名（用于关联上级数据） */
  parentKey?: string;
}

/**
 * 数据下探配置接口
 */
export interface DrillDownConfig {
  /** 是否启用数据下探 */
  enabled: boolean;
  /** 下探层级配置数组 */
  levels: DrillDownLevel[];
  /** 当前层级 */
  currentLevel: number;
  /** 最大层级数 */
  maxLevel: number;
  /** 异步数据提供者（可选） */
  dataProvider?: DrillDownDataProvider;
  /** 数据获取策略 */
  dataStrategy?: 'static' | 'async' | 'hybrid';
}

/**
 * 图表布局配置接口
 */
export interface ChartLayoutConfig {
  /** 占用的网格列数 (1-4) */
  columnSpan?: number;
  /** 最大宽度限制 */
  maxWidth?: string;
  /** 最小宽度限制 */
  minWidth?: string;
  /** 是否在宽屏下居中显示 */
  centerOnWideScreen?: boolean;
}

/**
 * 图表配置接口 - 复用 ECharts 类型
 */
export interface ChartConfig {
  /** 加载策略类型 */
  loadingStrategy: string;
  /** 图表唯一标识 */
  id: string;
  /** 图表类型 */
  type: 'bar' | 'line' | 'pie' | 'scatter' | 'radar' | 'funnel';
  /** 图表标题 */
  title: string;
  /** 数据源标识 */
  dataSource: string;
  /** ECharts配置选项 */
  options: EChartsOption;
  /** 数据下探配置（可选） */
  drillDown?: DrillDownConfig;
  /** 图表尺寸 - 支持数字(px)和字符串(%, vh, vw等) */
  size?: { width?: number | string; height?: number | string };
  /** 图表位置 */
  position?: { x?: number; y?: number };
  /** 图表布局配置 */
  layout?: ChartLayoutConfig;
  /** 自定义属性（可选） */
  customProps?: {
    /** 是否支持切换 */
    switchable?: boolean;
    /** 当前数据源 */
    currentDataSource?: string;
    /** 备选数据源 */
    alternativeDataSource?: string;
    /** 备选标题 */
    alternativeTitle?: string;
    /** 其他自定义属性 */
    [key: string]: any;
  };
}

/**
 * 统计数据项接口
 */
export interface StatisticsItem {
  /** 统计项ID，用于数据映射 */
  id?: string;
  /** 显示标题 */
  title: string;
  /** 总数值 */
  total: number | string;
  /** 🔥 重命名：环比数据（原increase，可能不存在） */
  chainComparison?: number;
  /** 🔥 重命名：环比趋势方向（原increaseTrend，可能不存在） */
  chainTrend?: boolean;
  /** 🔥 重命名：同比数据（原decrease，可能不存在） */
  yearComparison?: number;
  /** 🔥 重命名：同比趋势方向（原decreaseTrend，可能不存在） */
  yearTrend?: boolean;
  /** 是否为核心指标 */
  core?: boolean;
  /** 数据源标识 */
  dataSource?: string;
  /** 是否需要异步数据 */
  needsAsyncData?: boolean;
}

/**
 * 统计数据配置接口
 */
export interface StatisticsConfig {
  /** 统计组ID */
  id: string;
  /** 统计组标题 */
  title?: string;
  /** 数据源API标识 */
  dataSource: string;
  /** 统计项列表 */
  items: StatisticsItem[];
  /** 是否需要异步加载 */
  needsAsyncData: boolean;
  /** 加载策略类型 */
  loadingStrategy: string;
}

/**
 * 局部筛选器配置接口
 */
export interface LocalFilterConfig {
  /** 是否启用局部筛选器 */
  enabled: boolean;
  /** 显示的筛选字段 */
  fields: ('statPeriod' | 'periodRange')[];
}

/**
 * 图标组配置
 */
export interface TabConfigGroupConfig {
  /** 组唯一标识 */
  id: string;
  title: string;
  // 图表列表
  chartList: ChartConfig[];
  // 统计数据配置
  statisticsConfig?: StatisticsConfig;
  /** 局部筛选器配置 */
  localFilterConfig?: LocalFilterConfig;
}
/**
 * Tab配置接口
 */
export interface TabConfig {
  /** Tab唯一标识 */
  id: string;
  /** Tab显示名称 */
  name: string;
  /** 布局类型 */
  layout: 'grid' | 'flex' | 'custom';
  /** 权限编码 */
  auth?: string;
  /** 分组数据 */
  groups: TabConfigGroupConfig[];
}

/**
 * 看板状态管理接口
 */
export interface DashboardState {
  /** Tab配置数组 */
  tabs: TabConfig[];
  /** 当前激活的Tab */
  activeTab: string;
  /** 拖拽状态 */
  isDragging: boolean;
  /** 被拖拽的项目 */
  draggedItem: string | null;
  /** 导出状态 */
  isExporting: boolean;
}

/**
 * 数据下探状态接口
 */
export interface DrillDownState {
  /** 当前层级 */
  currentLevel: number;
  /** 各层级数据缓存 */
  levelData: Map<number, any[]>;
  /** 面包屑导航 */
  breadcrumb: string[];
  /** 原始顶层数据 */
  originalData: any[];
}

/**
 * 图表数据项接口
 */
export interface ChartDataItem {
  /** 数据项名称 */
  name: string;
  /** 数据项数值 - 支持单值或多维数组 */
  value: number | number[];
  /** 子级数据（用于下探） */
  children?: ChartDataItem[];
  /** 额外属性 */
  [key: string]: any;
}

/**
 * 图表事件参数接口
 */
export type ChartEventParams = {
  /** 事件类型 */
  type: string;
  /** 数据项 */
  data: ChartDataItem;
  /** 系列类型 */
  seriesType: string;
  /** 系列索引 */
  seriesIndex: number;
  /** 数据索引 */
  dataIndex: number;
};

/**
 * 图表组件Props接口
 */
export interface ChartComponentProps {
  /** 图表配置 */
  config: ChartConfig;
  /** 图表数据 */
  data: ChartDataItem[];
  /** 图表高度 */
  height?: string;
  /** 图表宽度 */
  width?: string;
  /** 是否可拖拽 */
  draggable?: boolean;
}

/**
 * 图表组件Emits接口
 */
export interface ChartComponentEmits {
  /** 数据下探事件 */
  'drill-down': [data: ChartDataItem, level: number];
  /** 图表点击事件 */
  'chart-click': [params: ChartEventParams];
  /** 图表双击事件 */
  'chart-dblclick': [params: ChartEventParams];
  /** 图表区域点击事件 */
  'chart-area-click': [event: any];
}

/**
 * 导出配置接口
 */
export interface ExportConfig {
  /** 导出文件名 */
  filename: string;
  /** 导出格式 */
  format: 'pdf' | 'png' | 'jpg';
  /** 导出质量（1-2） */
  scale: number;
}

/**
 * 图表操作上下文接口
 * 🔥 优化：集成状态管理，无需单独的状态上下文
 */
export interface ChartActionContext {
  /** 切换图表数据源 */
  switchChartDataSource: (chartId: string, newDataSource: string) => void;
  /** 刷新图表 */
  refreshChart: (chartId: string) => void;
  /** 导出图表 */
  exportChart: (chartId: string, format: string) => void;
  /** 全屏显示图表 */
  fullscreenChart: (chartId: string) => void;
  /** 通用下探处理函数 */
  handleDrillDown?: (data: ChartDataItem, chartConfig: any) => Promise<void>;
  /** 重置图表到顶层 */
  resetChartToTopLevel?: (chartConfig: any) => Promise<void>;

  // 🔥 新增：内置状态管理
  /** 图表loading状态 */
  chartLoadingStates: Record<string, boolean>;
  /** 图表错误状态 */
  chartErrorStates: Record<string, string | null>;
  /** 设置图表loading状态 */
  setChartLoading: (chartId: string, loading: boolean) => void;
  /** 设置图表错误状态 */
  setChartError: (chartId: string, error: string | null) => void;
}

/**
 * 图表数据上下文接口
 */
export interface ChartDataContext {
  /** 获取图表数据 */
  getChartData: (dataSource: string) => ChartDataItem[];
  /** 设置图表数据 */
  setChartData: (dataSource: string, data: ChartDataItem[]) => void;
  /** 刷新图表数据 */
  refreshChartData: (chartId: string) => Promise<void>;
  /** 清除数据缓存 */
  clearDataCache: () => void;
}

/**
 * 图表配置上下文接口
 */
export interface ChartConfigContext {
  /** 获取图表配置 */
  getChartConfig: (chartId: string) => ChartConfig | undefined;
  /** 获取所有Tab配置 */
  getAllTabs: () => TabConfig[];
  /** 获取图表下探状态 */
  getChartDrillDownState: (chartId: string) => { currentLevel: number; isInDrillDown: boolean } | null;
}

/**
 * 图表事件上下文接口
 */
export interface ChartEventContext {
  /** 图表点击事件 */
  onChartClick: (params: ChartEventParams) => void;
  /** 数据下探事件 */
  onDrillDown: (data: ChartDataItem, level: number, chartConfig?: any) => Promise<void>;
  /** 数据点击事件 */
  onDataClick: (data: ChartDataItem) => void;
  /** 图表双击事件 */
  onChartDblClick: (params: ChartEventParams) => void;
  /** 图表区域点击事件 */
  onChartAreaClick: (event: any) => void;
}

/**
 * 图表状态上下文接口
 */
export interface ChartStateContext {
  /** 全局loading状态 */
  loading: boolean;
  /** 图表loading状态 */
  chartLoadingStates: Record<string, boolean>;
  /** 设置图表loading状态 */
  setChartLoading: (chartId: string, loading: boolean) => void;
}

/**
 * 统计数据操作上下文接口
 * 🔥 优化：集成状态管理，无需单独的状态上下文
 */
export interface StatisticsActionContext {
  /** 刷新统计数据 */
  refreshStatistics: (configId: string) => Promise<void>;
  /** 重新加载所有统计数据 */
  reloadAllStatistics: () => Promise<void>;

  // 🔥 新增：内置状态管理
  /** 统计数据loading状态 */
  statisticsLoadingStates: Record<string, boolean>;
  /** 统计数据错误状态 */
  statisticsErrorStates: Record<string, string | null>;
  /** 设置统计数据loading状态 */
  setStatisticsLoading: (configId: string, loading: boolean) => void;
  /** 设置统计数据错误状态 */
  setStatisticsError: (configId: string, error: string | null) => void;
}

/**
 * 统计数据上下文接口
 */
export interface StatisticsDataContext {
  /** 获取统计数据 */
  getStatisticsData: (configId: string) => StatisticsItem[];
  /** 设置统计数据 */
  setStatisticsData: (configId: string, data: StatisticsItem[]) => void;
  /** 刷新统计数据 */
  refreshStatisticsData: (configId: string) => Promise<void>;
  /** 清除统计数据缓存 */
  clearStatisticsCache: () => void;
  /** 获取统计配置 */
  getStatisticsConfig: (configId: string) => StatisticsConfig | null;
}

/**
 * 统计数据状态上下文接口
 */
export interface StatisticsStateContext {
  /** 全局统计数据loading状态 */
  loading: boolean;
  /** 统计配置loading状态 */
  statisticsLoadingStates: Record<string, boolean>;
  /** 设置统计配置loading状态 */
  setStatisticsLoading: (configId: string, loading: boolean) => void;
  /** 统计数据错误状态 */
  statisticsErrorStates: Record<string, string | null>;
  /** 设置统计数据错误状态 */
  setStatisticsError: (configId: string, error: string | null) => void;
}

/**
 * 通用的月度数据接口
 */
export interface MonthlyChannelData {
  name: string; // 月份名称
  value: number; // 总值
  type: 'period';
  channels: Record<string, number>; // 各渠道数据
  subChannels?: Record<string, Record<string, number>>; // 子渠道数据
}

/**
 * 渠道配置定义 - 通用的渠道层级配置
 */
export interface ChannelConfig {
  key: string;
  name: string;
  color: string;
  children?: ChannelConfig[];
}
