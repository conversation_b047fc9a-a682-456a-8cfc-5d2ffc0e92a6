# 复杂图表数据处理最佳实践

## 概述

本文档总结了用户分析图表的数据处理方案，为后续类似的复杂图表提供参考。

## 问题背景

用户分析图表需要同时展示：
- 堆叠柱状图：各渠道的用户新增数据
- 折线图：用户新增/注销趋势
- 复杂tooltip：按用户新增/注销分组展示各渠道数量

**核心挑战**：需要在tooltip中展示比标准ChartDataItem[]更丰富的结构化数据。

## 解决方案

### 1. 数据嵌入传递策略

**核心思想**：将结构化数据作为ChartDataItem的customData一部分传递，保持架构一致性。

```typescript
// 在数据转换层嵌入结构化元数据
chartDataItem.customData = {
  ...chartDataItem.customData,
  _structuredMetadata: {
    dates: sortedDates,
    registerRawMap,
    logoffRawMap,
  },
};
```

### 2. 数据流程设计

```
API响应 → transformData → ChartDataItem[]（嵌入结构化元数据）
                              ↓
策略层正常传递 → updateChartConfig → 从ChartDataItem中提取结构化数据
```

### 3. 关键实现

#### 数据转换层（utils/dataTransform.ts）
```typescript
export function transformUserAnalysisChartDataToChartData(apiResponse): ChartDataItem[] {
  // 1. 一次性构建完整的结构化数据
  const registerMap = new Map<string, ChartDataItem>();
  const logoffMap = new Map<string, ChartDataItem>();

  // 2. 创建数据项并构建映射
  registerList.forEach((item) => {
    const chartDataItem = createUserAnalysisChartDataItem(item.statisticsDate, item, 'REGISTER');
    registerMap.set(item.statisticsDate, chartDataItem);
    chartData.push(chartDataItem);
  });

  // 3. 将完整结构化数据嵌入到第一个数据项中
  chartData[0].customData._structuredMetadata = {
    dates: sortedDates,
    registerMap,
    logoffMap,
  };

  return chartData;
}
```

#### 配置更新层（config/userAnalysisChartConfig.ts）
```typescript
export const updateUserAnalysisChartData = (baseConfig: ChartConfig, chartData: ChartDataItem[]) => {
  // 直接从第一个数据项中获取结构化元数据
  const { dates, registerMap, logoffMap } = chartData[0].customData._structuredMetadata;

  // 直接使用已构建的映射更新图表配置
  // ...
}
```

#### 策略层（strategies/ChartDataLoadingStrategy.ts）
```typescript
protected async transformData(apiData): Promise<ChartDataItem[]> {
  return transformUserAnalysisChartDataToChartData(apiData);
}

async updateChartConfig(chartConfig: ChartConfig, data: ChartDataItem[]): Promise<Partial<ChartConfig>> {
  return updateUserAnalysisChartData(chartConfig, data);
}
```

## 架构优势

### ✅ 保持架构一致性
- 遵循标准的 `transformData` → `updateChartConfig` 流程
- 无状态存储，符合函数式编程原则
- 接口签名保持一致，不破坏现有设计

### ✅ 数据传递清晰
- 通过函数参数正常传递，无隐式依赖
- 结构化数据嵌入在标准数据结构中
- 易于调试和测试

### ✅ 性能优化
- **一次性映射构建**：在数据转换层直接构建完整的Map结构，避免重复映射
- **零重复计算**：配置更新层直接使用已构建的映射，无需重新遍历数据
- **内存使用高效**：结构化数据只存储在第一个数据项中，避免重复存储
- **Tooltip逻辑统一**：消除重复的渠道数据获取逻辑，统一使用customData

## 适用场景

此方案适用于以下场景：
1. **复杂tooltip需求**：需要展示比标准数据更丰富的信息
2. **多数据源组合**：需要同时处理多种类型的数据
3. **架构约束**：需要保持现有架构设计不变
4. **性能要求**：需要避免重复的数据处理

## 注意事项

1. **元数据命名**：使用 `_structuredMetadata` 前缀避免与业务数据冲突
2. **数据一致性**：确保所有ChartDataItem都包含相同的元数据结构
3. **内存考虑**：对于大数据量场景，考虑元数据的内存占用
4. **向后兼容**：确保不影响现有图表的正常功能

## 总结

通过数据嵌入传递策略，我们成功解决了复杂图表的数据处理问题，既满足了功能需求，又保持了架构的纯净性。这个方案可以作为后续类似复杂图表的参考模板。
