# 筛选器权限级别动态调整方案

## 修复日期
2025-08-25

## 需求背景

在统计看板中，不同Tab对筛选器的权限处理有不同的需求：
- **userStatistics Tab**：需要将权限处理限制到国家层级，即使是厂端账户也不显示省份和城市字段
- **其他Tab**：保持原有的权限处理逻辑（厂端账户处理到城市层级）

## 设计方案

### 核心思路
在现有权限处理逻辑中添加Tab感知能力，根据当前活跃Tab动态调整权限处理深度。

### 架构特点
- ✅ **最小化改动**：在现有架构基础上进行增强，不破坏原有逻辑
- ✅ **Tab感知**：根据当前活跃Tab动态调整权限级别
- ✅ **自动切换**：Tab切换时自动重新应用权限逻辑
- ✅ **向后兼容**：保持其他Tab的原有行为不变

## 实现细节

### 1. 权限级别定义
```typescript
/**
 * 获取当前Tab下的权限处理级别
 * @returns 'COUNTRY' | 'FULL'
 */
const getPermissionLevel = () => {
  return activeTabId.value === 'userStatistics' ? 'COUNTRY' : 'FULL';
};
```

### 2. 权限处理逻辑调整
在 `handleUserDataAuth` 方法中添加Tab判断：
```typescript
// 非厂端账户只处理到国家/地区层级
if (accountCategory !== AccountTypeEnum.FACTORY_END) {
  return;
}

// 🔥 新增：用户统计Tab下，即使是厂端账户也只处理到国家层级
if (getPermissionLevel() === 'COUNTRY') {
  return;
}
```

### 3. 字段联动逻辑优化
在 `handleFieldChange` 的国家字段处理中添加权限级别判断：
```typescript
if (field === 'countryCode') {
  // 🔥 新增：用户统计Tab下不显示省份和城市字段
  if (getPermissionLevel() === 'COUNTRY') {
    // 隐藏省份和城市字段
    formMethods.updateSchema([
      { field: 'provinceCode', ifShow: false },
      { field: 'cityCode', ifShow: false },
    ]);
    // 清空省份和城市的值
    formMethods.setFieldsValue({
      provinceCode: undefined,
      cityCode: undefined,
    });
    return;
  }
  // ... 原有逻辑
}
```

### 4. Tab切换监听
添加Tab切换监听，自动重新应用权限：
```typescript
watch(
  () => activeTabId.value,
  (newTabId, oldTabId) => {
    if (enableUserDataAuth && isInitialized.value && newTabId !== oldTabId) {
      console.log(`🔄 Tab切换: ${oldTabId} -> ${newTabId}，重新应用权限逻辑`);
      handleUserDataAuth();
    }
  }
);
```

## 修改文件

- `src/views/statisticsDashboard/hooks/useFilters.ts`

## 核心修改点

### 1. 添加权限级别判断方法 (第401-407行)
- 根据当前Tab返回权限处理级别
- userStatistics Tab返回 'COUNTRY'，其他Tab返回 'FULL'

### 2. 修改权限处理逻辑 (第521-529行)  
- 在厂端账户权限处理中添加Tab判断
- userStatistics Tab下即使是厂端账户也只处理到国家层级

### 3. 优化字段联动逻辑 (第741-793行)
- 在国家字段变化时根据权限级别决定是否显示省份和城市字段
- userStatistics Tab下强制隐藏省份和城市字段

### 4. 添加Tab切换监听 (第818-833行)
- 监听activeTabId变化
- Tab切换时重新应用权限逻辑

### 5. 增强调试日志 (第481-488行)
- 添加权限处理过程的调试信息
- 便于追踪和排查问题

## 预期效果

### userStatistics Tab
- ✅ 厂端账户只显示到国家层级字段
- ✅ 经销商和网点账户保持原有行为
- ✅ 省份和城市字段被隐藏
- ✅ 切换到该Tab时自动应用国家级权限

### 其他Tab  
- ✅ 保持原有权限处理逻辑不变
- ✅ 厂端账户正常显示省份和城市字段
- ✅ 切换到其他Tab时恢复完整权限处理

### 切换行为
- ✅ Tab切换时自动重新应用对应的权限逻辑
- ✅ 字段显示/隐藏状态正确切换
- ✅ 表单值正确清理和设置

## 技术要点

1. **Tab感知设计**：通过监听activeTabId实现Tab感知的权限处理
2. **权限级别抽象**：将权限处理级别抽象为配置，便于扩展
3. **最小化改动**：在现有逻辑基础上增强，保持架构稳定性
4. **自动化处理**：Tab切换时自动重新应用权限，无需手动干预
5. **向后兼容**：确保其他Tab的行为完全不受影响
