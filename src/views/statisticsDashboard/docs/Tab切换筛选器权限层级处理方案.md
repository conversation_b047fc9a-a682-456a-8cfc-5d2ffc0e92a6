# Tab切换筛选器权限层级处理方案

## 实施日期
2025-08-25

## 需求背景

在统计看板中，当前Tab是 `userStatistics`（用户统计）时，需要对筛选器的数据权限处理到国家层级即可，当切换到别的Tab时又要恢复原有的权限层级显示。

### 核心要求：
1. **最小化改动**：尽可能不改动现有架构
2. **权限处理只执行一次**：保持现有的 `handleUserDataAuth()` 逻辑不变
3. **保留表单数据**：保留当前Tab页的筛选表单数据值以及隐藏项
4. **按需切换**：切换到用户统计时按需隐藏，切回去时重新恢复

## 设计方案

### 核心思路
采用**状态保存 + 监听切换**的模式：
1. 在权限处理完成后，保存字段的原始显示状态
2. 监听 `activeTabId` 变化，根据Tab类型切换显示模式
3. 用户统计模式只显示到国家层级，其他模式恢复原始状态
4. 只控制字段显示/隐藏，不修改表单数据

### 技术实现

#### 1. 状态管理
```typescript
// 字段状态接口
interface FieldState {
  ifShow: boolean | ((renderCallbackParams: any) => boolean);
  componentProps?: any;
}

// 原始字段状态（权限处理完成后的状态）
const originalFieldStates = ref<Record<string, FieldState>>({});

// 是否处于用户统计模式
const isUserStatisticsMode = ref(false);
```

#### 2. 核心方法

**保存原始状态**：
```typescript
const saveOriginalFieldStates = () => {
  const fieldsToSave = ['provinceCode', 'cityCode', 'dealerCode'];

  fieldsToSave.forEach((fieldName) => {
    // 从初始的 formSchemas 中获取字段的当前状态
    // 这个时候字段状态已经被 handleUserDataAuth 处理过了
    const schema = formSchemas.value.find((s) => s.field === fieldName);
    if (schema) {
      originalFieldStates.value[fieldName] = {
        ifShow: schema.ifShow || false,
        componentProps: { ...schema.componentProps },
      };
    }
  });
};
```

**应用用户统计模式**：
```typescript
const applyUserStatisticsMode = () => {
  if (isUserStatisticsMode.value) return; // 避免重复应用

  console.log('🔄 切换到用户统计模式，隐藏省份/城市/经销商字段');

  // 隐藏省份、城市、经销商字段 - 使用 formMethods.updateSchema
  const fieldsToHide = ['provinceCode', 'cityCode', 'dealerCode'];

  fieldsToHide.forEach((fieldName) => {
    formMethods?.updateSchema({
      field: fieldName,
      ifShow: false,
    });
  });

  isUserStatisticsMode.value = true;
};
```

**恢复原始模式**：
```typescript
const restoreOriginalMode = () => {
  if (!isUserStatisticsMode.value) return; // 避免重复恢复

  console.log('🔄 恢复原始模式，根据权限显示字段');

  // 根据原始状态恢复字段显示 - 使用 formMethods.updateSchema
  Object.keys(originalFieldStates.value).forEach((fieldName) => {
    const originalState = originalFieldStates.value[fieldName];
    if (originalState) {
      formMethods?.updateSchema({
        field: fieldName,
        ifShow: originalState.ifShow,
        componentProps: originalState.componentProps,
      });
    }
  });

  isUserStatisticsMode.value = false;
};
```

#### 3. Tab切换监听
```typescript
watch(
  () => activeTabId.value,
  (newTabId) => {
    console.log('📋 Tab切换:', newTabId);
    handleTabModeChange(newTabId);
  },
  { immediate: false } // 不立即执行，等待初始化完成
);

const handleTabModeChange = (tabId: string) => {
  // 确保初始化完成且有原始状态数据
  if (!isInitialized.value || Object.keys(originalFieldStates.value).length === 0) {
    return;
  }
  
  if (tabId === 'userStatistics') {
    applyUserStatisticsMode();
  } else {
    restoreOriginalMode();
  }
};
```

#### 4. 集成点
在 `handleUserDataAuth()` 完成后调用保存原始状态：
```typescript
// 标记初始化完成
isInitialized.value = true;

// 🔥 新增：保存原始字段状态，用于Tab切换时的模式切换
saveOriginalFieldStates();

if (initializationResolve) {
  initializationResolve(params);
}
```

## 实现效果

### 1. 用户统计Tab
- 筛选器只显示：统计周期、时间范围、大区、国家
- 隐藏：省份、城市、经销商字段
- 保持表单数据不变

### 2. 其他Tab
- 根据用户权限显示完整的筛选层级
- 恢复原始的字段显示状态
- 保持表单数据不变

### 3. 切换流程
1. 初始化时执行权限处理，保存原始状态
2. 切换到用户统计Tab → 隐藏省份/城市/经销商字段
3. 切换到其他Tab → 恢复原始权限显示状态
4. 表单数据在整个过程中保持不变

## 技术优势

1. **最小化改动**：只在 `useFilters` hook 中添加约80行代码
2. **架构兼容**：完全兼容现有的权限处理架构
3. **性能优化**：权限处理只执行一次，后续只是显示/隐藏切换
4. **数据完整性**：表单数据在Tab切换过程中保持完整
5. **状态一致性**：通过状态管理确保切换的一致性
6. **调试友好**：添加了详细的日志输出

## 相关文件

- `src/views/statisticsDashboard/hooks/useFilters.ts` - 主要实现文件
- `src/views/statisticsDashboard/hooks/useTabConfigManager.ts` - Tab管理器（提供activeTabId）

## 注意事项

1. **Schema更新方式**：必须使用 `formMethods.updateSchema` 方法更新表单配置，禁止直接修改 `formSchemas`
2. **初始化顺序**：确保在权限处理完成后才开始监听Tab变化
3. **状态保护**：通过标志位避免重复应用相同模式
4. **边界处理**：处理快速切换Tab和初始化未完成的情况
5. **类型安全**：正确处理 `ifShow` 字段的函数类型

## 关键技术点

### 表单Schema更新规范
```typescript
// ✅ 正确方式：使用 formMethods.updateSchema
formMethods?.updateSchema({
  field: fieldName,
  ifShow: false,
});

// ❌ 错误方式：直接修改 formSchemas
formSchemas.value.find(s => s.field === fieldName).ifShow = false;
```

### 状态保存时机
状态保存必须在 `handleUserDataAuth()` 完成后进行，此时字段的权限状态已经被正确设置。
