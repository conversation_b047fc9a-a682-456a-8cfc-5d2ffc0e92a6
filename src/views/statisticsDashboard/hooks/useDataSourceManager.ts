/**
 * 全局数据源管理器
 * 实现全局单例模式，统一管理数据源映射和数据获取逻辑
 * 解决多实例化和数据不同步问题
 */

import { reactive, computed, readonly } from 'vue';
import type { ComputedRef } from 'vue';
import type { ChartDataItem, StatisticsItem } from '../types/statisticDashboard';

/**
 * 数据源元数据接口
 */
export interface DataSourceMeta {
  name: string;
  description: string;
  category: string;
  type: 'chart' | 'statistics';
  dataType: 'ChartDataItem' | 'StatisticsItem';
  lastUpdated: Date;
}

/**
 * 数据源管理器接口
 */
export interface IDataSourceManager {
  // 数据访问
  getDataSource: <T = ChartDataItem>(dataSourceKey: string) => T[];
  setDataSource: <T = ChartDataItem>(dataSourceKey: string, data: T[], meta?: Partial<DataSourceMeta>) => void;
  updateDataSource: (dataSourceKey: string, data: ChartDataItem[]) => void;
  removeDataSource: (dataSourceKey: string) => void;

  // 元数据
  getDataSourceMeta: (dataSourceKey: string) => DataSourceMeta | undefined;
  getAllDataSourceKeys: () => string[];
  getDataSourcesByCategory: <T = ChartDataItem>(category: string) => Record<string, T[]>;

  // 管理方法
  clearAllDataSources: () => void;
  resetToDefaults: () => void;

  // 只读属性
  readonly dataSourceMap: Readonly<Record<string, ChartDataItem[] | StatisticsItem[]>>;
  readonly dataSourceMeta: Readonly<Record<string, DataSourceMeta>>;
  readonly dataSourceStats: ComputedRef<{
    totalDataSources: number;
    totalCategories: number;
    categories: string[];
  }>;
  readonly dataSourcesByCategory: ComputedRef<Record<string, string[]>>;
}

/**
 * 创建数据源管理器实例
 */
function createDataSourceManager(): IDataSourceManager {
  // 🔥 核心：使用 reactive 创建响应式数据源映射
  const dataSourceMap = reactive<Record<string, ChartDataItem[] | StatisticsItem[]>>({});

  // 数据源元数据
  const dataSourceMeta = reactive<Record<string, DataSourceMeta>>({});

  /**
   * 🔥 优化：统一的数据源获取方法，支持泛型
   */
  const getDataSource = <T = ChartDataItem>(dataSourceKey: string): T[] => {
    return (dataSourceMap[dataSourceKey] || []) as T[];
  };

  /**
   * 🔥 优化：统一的数据源设置方法，支持多种数据类型
   */
  const setDataSource = <T = ChartDataItem>(dataSourceKey: string, data: T[], meta?: Partial<DataSourceMeta>) => {
    dataSourceMap[dataSourceKey] = data as ChartDataItem[] | StatisticsItem[];

    if (meta) {
      dataSourceMeta[dataSourceKey] = {
        name: meta.name || dataSourceKey,
        description: meta.description || '',
        category: meta.category || 'custom',
        type: meta.type || 'chart',
        dataType: meta.dataType || 'ChartDataItem', // 🔥 提供默认值
        lastUpdated: new Date(),
        ...meta,
      };
    }
  };

  /**
   * 更新数据源数据
   */
  const updateDataSource = (dataSourceKey: string, data: ChartDataItem[]) => {
    if (dataSourceMap[dataSourceKey]) {
      dataSourceMap[dataSourceKey] = data;

      if (dataSourceMeta[dataSourceKey]) {
        dataSourceMeta[dataSourceKey].lastUpdated = new Date();
      }
    }
  };

  /**
   * 删除数据源
   */
  const removeDataSource = (dataSourceKey: string) => {
    delete dataSourceMap[dataSourceKey];
    delete dataSourceMeta[dataSourceKey];
  };

  /**
   * 获取数据源元数据
   */
  const getDataSourceMeta = (dataSourceKey: string) => {
    return dataSourceMeta[dataSourceKey];
  };

  /**
   * 获取所有数据源键名
   */
  const getAllDataSourceKeys = (): string[] => {
    return Object.keys(dataSourceMap);
  };

  /**
   * 🔥 优化：按分类获取数据源，支持泛型
   */
  const getDataSourcesByCategory = <T = ChartDataItem>(category: string): Record<string, T[]> => {
    const result: Record<string, T[]> = {};

    Object.entries(dataSourceMeta).forEach(([key, meta]) => {
      if (meta.category === category) {
        result[key] = dataSourceMap[key] as T[];
      }
    });

    return result;
  };

  /**
   * 清空所有数据源
   */
  const clearAllDataSources = () => {
    Object.keys(dataSourceMap).forEach((key) => delete dataSourceMap[key]);
    Object.keys(dataSourceMeta).forEach((key) => delete dataSourceMeta[key]);
  };

  /**
   * 重置为默认数据源
   */
  const resetToDefaults = () => {
    clearAllDataSources();
    // initializeDefaultDataSources();
  };

  // 计算属性：数据源统计
  const dataSourceStats = computed(() => {
    const categories = new Set(Object.values(dataSourceMeta).map((meta) => meta.category));

    return {
      totalDataSources: Object.keys(dataSourceMap).length,
      totalCategories: categories.size,
      categories: Array.from(categories),
    };
  });

  // 计算属性：按分类分组的数据源
  const dataSourcesByCategory = computed(() => {
    const grouped: Record<string, string[]> = {};

    Object.entries(dataSourceMeta).forEach(([key, meta]) => {
      if (!grouped[meta.category]) {
        grouped[meta.category] = [];
      }
      grouped[meta.category].push(key);
    });

    return grouped;
  });

  // 初始化默认数据源
  // initializeDefaultDataSources();

  return {
    // 状态（只读）
    dataSourceMap: readonly(dataSourceMap) as Readonly<Record<string, ChartDataItem[] | StatisticsItem[]>>,
    dataSourceMeta: readonly(dataSourceMeta) as Readonly<Record<string, DataSourceMeta>>,
    dataSourceStats,
    dataSourcesByCategory,

    // 🔥 优化：统一的基础方法（支持泛型）
    getDataSource,
    setDataSource,
    updateDataSource,
    removeDataSource,
    getDataSourceMeta,
    getAllDataSourceKeys,
    getDataSourcesByCategory,
    clearAllDataSources,
    resetToDefaults,
  };
}

/**
 * 🔥 全局单例数据源管理器实例
 * 确保整个应用共享同一个数据源管理器
 */
const globalDataSourceManager = createDataSourceManager();

/**
 * 获取全局数据源管理器（推荐使用）
 * @returns 全局单例数据源管理器实例
 */
export function useGlobalDataSourceManager(): IDataSourceManager {
  return globalDataSourceManager;
}

/**
 * @deprecated 请使用 useGlobalDataSourceManager 替代
 * 为了向后兼容保留，但会返回全局单例实例
 */
export function useDataSourceManager(): IDataSourceManager {
  console.warn('useDataSourceManager 已废弃，请使用 useGlobalDataSourceManager');
  return globalDataSourceManager;
}

// 默认导出全局数据源管理器
export default useGlobalDataSourceManager;

// 导出类型
export type { ChartDataItem, StatisticsItem } from '../types/statisticDashboard';
