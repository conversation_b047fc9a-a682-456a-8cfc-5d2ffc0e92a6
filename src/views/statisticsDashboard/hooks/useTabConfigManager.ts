/**
 * Tab配置管理Hook
 * 统一管理Tab配置，替代mock/data.ts中的配置管理
 * 🔥 修复：使用单例模式确保状态在所有组件间共享
 */

import { reactive, computed, readonly, ref, provide, inject, type InjectionKey } from 'vue';
import type { TabConfig, ChartConfig, StatisticsConfig, StatisticsItem, TabConfigGroupConfig } from '../types/statisticDashboard';
import { AuthEnum } from '../../../enums/authEnum';
import { useI18n } from '../../../hooks/web/useI18n';
import { usePermission } from '../../../hooks/web/usePermission';

// 导入具体的图表配置
import { sourceOfCluesChartConfig } from '../config/clueSourceChartConfig';
import { utmChartConfig } from '../config/utmChartConfig';
import { clueEffectivenessChartConfig } from '../config/clueEffectivenessChartConfig';
import { followUpStatusChartConfig } from '../config/followUpStatusChartConfig';
import { leadConversionChartConfig } from '../config/leadConversionChartConfig';
import { firstFollowUpTimeAnalysisChartConfig } from '../config/FirstFollowUpTimeAnalysisChartConfig';
import { firstResponseTimeoutChartConfig } from '../config/firstResponseTimeoutChartConfig';
// 导入统计数据配置
import { clueOverviewStatisticsConfig, leadConversionStatisticsConfig, userAnalysisStatisticsConfig } from '../config/statisticsConfig';
import { vehicleModelDealChartConfig } from '../config/vehicleModelDealChartConfig';
import { userAnalysisChartConfig } from '../config/userAnalysisChartConfig';

const { t } = useI18n('common');

// 🔥 上下文Key定义
export const TAB_CONFIG_CONTEXT_KEY: InjectionKey<ReturnType<typeof createTabConfigManager>> = Symbol('tabConfigContext');

// 🔥 修复：全局单例状态，确保所有组件共享相同的状态
let globalTabConfigManager: ReturnType<typeof createTabConfigManager> | null = null;

/**
 * 创建Tab配置管理器实例
 */
function createTabConfigManager() {
  const { hasPermission } = usePermission();

  // Tab配置存储
  const tabConfigs = reactive<Record<string, TabConfig>>({});

  // 图表配置存储
  const chartConfigs = reactive<Record<string, ChartConfig>>({});

  // Tab状态管理
  const activeTabId = ref<string>('');

  // 🔥 新增：Tab级别的loading状态管理
  const tabLoadingStates = reactive<Record<string, boolean>>({});

  /**
   * 设置Tab的loading状态
   */
  const setTabLoading = (tabId: string, loading: boolean) => {
    tabLoadingStates[tabId] = loading;
    console.log(`🔥 设置Tab[${tabId}]的loading状态: ${loading}`);
  };

  /**
   * 获取Tab的loading状态
   */
  const getTabLoading = (tabId: string): boolean => {
    return tabLoadingStates[tabId] || false;
  };

  /**
   * 获取当前激活Tab的loading状态
   */
  const getCurrentTabLoading = (): boolean => {
    return getTabLoading(activeTabId.value);
  };

  /**
   * 初始化默认Tab配置
   */
  const initializeDefaultTabs = () => {
    const defaultTabs: TabConfig[] = [
      {
        id: 'clueStatistics',
        name: t('clueStatistics'),
        layout: 'grid',
        groups: [
          {
            id: 'clue-overview-group',
            title: t('overviewOfClues'),
            statisticsConfig: clueOverviewStatisticsConfig,
            chartList: [],
          },
          {
            // 线索来源 - 🔥 示例：启用局部筛选器
            id: 'clue-source-group',
            title: t('sourceOfClues'),
            chartList: [sourceOfCluesChartConfig, utmChartConfig, clueEffectivenessChartConfig],
            // 新增：局部筛选器配置示例
            localFilterConfig: {
              enabled: false,
              fields: ['statPeriod', 'periodRange'],
            },
          },
          {
            id: 'clue-followup-group',
            title: t('followUpOnClues'),
            chartList: [followUpStatusChartConfig, firstFollowUpTimeAnalysisChartConfig, firstResponseTimeoutChartConfig],
          },
          {
            id: 'lead-conversion-group',
            title: t('leadConversion'),
            statisticsConfig: leadConversionStatisticsConfig,
            chartList: [leadConversionChartConfig, vehicleModelDealChartConfig],
            // 新增：局部筛选器配置示例
            localFilterConfig: {
              enabled: true,
              fields: ['statPeriod', 'periodRange'],
            },
          },
        ],
        auth: AuthEnum.View_Clue_Statistics,
      },
      {
        id: 'workOrderStatistics',
        name: t('workOrderStatistics'),
        layout: 'grid',
        groups: [],
        auth: AuthEnum.View_Work_Order_Statistics,
      },
      {
        id: 'vehicleStatistics',
        name: t('vehicleStatistics'),
        layout: 'grid',
        groups: [],
        auth: AuthEnum.View_Vehicle_Statistics,
      },
      {
        id: 'userStatistics',
        name: t('userStatistics'),
        layout: 'grid',
        groups: [
          {
            id: 'user-analysis-group',
            title: t('userAnalysis'),
            statisticsConfig: userAnalysisStatisticsConfig,
            chartList: [userAnalysisChartConfig],
          },
        ],
        auth: AuthEnum.View_User_Statistics,
      },
    ];

    // 存储Tab配置
    defaultTabs.forEach((tab) => {
      tabConfigs[tab.id] = tab;

      // 同时存储图表配置
      tab.groups?.forEach((group) => {
        group.chartList?.forEach((chart) => {
          chartConfigs[chart.id] = chart;
        });
      });
    });
  };

  /**
   * 获取所有Tab配置
   */
  const getAllTabs = (): TabConfig[] => {
    return Object.values(tabConfigs).filter((tab) => !tab.auth || hasPermission(tab.auth));
  };

  /**
   * 获取指定Tab配置
   */
  const getTabConfig = (tabId: string): TabConfig | undefined => {
    const tab = tabConfigs[tabId];
    if (!tab) return undefined;

    // 检查权限
    if (tab.auth && !hasPermission(tab.auth)) {
      return undefined;
    }

    return tab;
  };

  /**
   * 获取图表配置
   */
  const getChartConfig = (chartId: string): ChartConfig | undefined => {
    return chartConfigs[chartId];
  };

  /**
   * 根据图表ID或者统计数据ID获取所属的组ID
   */
  const getGroupIdByChartIdOrStatisticsId = (id: string): string | undefined => {
    for (const tab of Object.values(tabConfigs)) {
      for (const group of tab.groups || []) {
        if (group.chartList?.some((chart) => chart.id === id)) {
          return group.id;
        }
        if (group.statisticsConfig?.id === id) {
          return group.id;
        }
      }
    }
    return undefined;
  };

  /**
   * 获取组配置
   */
  const getGroupConfig = (groupId: string): { group: TabConfigGroupConfig; tabId: string } => {
    for (const tab of Object.values(tabConfigs)) {
      for (const group of tab.groups || []) {
        if (group.id === groupId) {
          return {
            group,
            tabId: tab.id,
          };
        }
      }
    }
    throw new Error('Group not found');
  };

  /**
   * 更新图表配置
   */
  const updateChartConfig = (chartId: string, updates: Partial<ChartConfig>) => {
    if (chartConfigs[chartId]) {
      Object.assign(chartConfigs[chartId], updates);
    }
  };

  /**
   * 获取当前Tab下的所有统计数据
   */
  const getCurrentTabStatistics = (): StatisticsConfig[] => {
    const tab = getTabConfig(activeTabId.value);
    if (!tab) return [];
    return tab.groups?.flatMap((group) => group.statisticsConfig || []) || [];
  };

  /**
   * 添加新的Tab配置
   */
  const addTabConfig = (tab: TabConfig) => {
    tabConfigs[tab.id] = tab;

    // 同时添加图表配置
    tab.groups?.forEach((group) => {
      group.chartList?.forEach((chart) => {
        chartConfigs[chart.id] = chart;
      });
    });
  };

  /**
   * 获取Tab中的所有图表配置
   */
  const getTabCharts = (tabId: string): ChartConfig[] => {
    const tab = getTabConfig(tabId);
    if (!tab) return [];

    return tab.groups?.flatMap((group) => group.chartList || []) || [];
  };

  /**
   * 重置所有配置
   */
  const resetConfigs = () => {
    Object.keys(tabConfigs).forEach((key) => delete tabConfigs[key]);
    Object.keys(chartConfigs).forEach((key) => delete chartConfigs[key]);
    activeTabId.value = '';
    initializeDefaultTabs();
  };

  /**
   * 设置激活的Tab
   */
  const setActiveTab = (tabId: string) => {
    const tab = getTabConfig(tabId);
    if (tab) {
      activeTabId.value = tabId;
      return true;
    }
    return false;
  };

  /**
   * 获取当前激活的Tab
   */
  const getActiveTab = (): TabConfig | undefined => {
    return getTabConfig(activeTabId.value);
  };

  /**
   * 获取当前激活Tab的图表列表
   */
  const getActiveTabCharts = (): ChartConfig[] => {
    const tab = getActiveTab();
    return tab?.groups?.flatMap((group) => group.chartList || []) || [];
  };

  /**
   * 初始化默认激活Tab
   */
  const initializeActiveTab = () => {
    const tabs = getAllTabs();
    if (tabs.length > 0 && !activeTabId.value) {
      activeTabId.value = tabs[0].id;
    }
  };

  // ========== 统计数据管理方法 ==========

  /**
   * 获取统计配置
   */
  function getStatisticsConfig(configId: string): StatisticsConfig | null {
    for (const tab of Object.values(tabConfigs)) {
      for (const group of tab.groups) {
        if (group.statisticsConfig?.id === configId) {
          return group.statisticsConfig;
        }
      }
    }
    return null;
  }

  /**
   * 更新统计数据
   */
  function updateStatisticsData(configId: string, statisticsData: StatisticsItem[]): void {
    for (const tab of Object.values(tabConfigs)) {
      for (const group of tab.groups) {
        if (group.statisticsConfig?.id === configId) {
          // 更新统计配置中的数据
          group.statisticsConfig.items = statisticsData;
          // console.log(`✅ 更新统计数据: ${configId}, 数据条数: ${statisticsData.length}`);
          return;
        }
      }
    }
    console.warn(`⚠️ 未找到统计配置: ${configId}`);
  }

  // 计算属性：可用的Tab列表
  const availableTabs = computed(() => getAllTabs());

  // 计算属性：当前激活的Tab
  const activeTab = computed(() => getActiveTab());

  // 计算属性：当前激活Tab的图表列表
  const activeTabCharts = computed(() => getActiveTabCharts());

  // 计算属性：配置统计
  const configStats = computed(() => ({
    totalTabs: Object.keys(tabConfigs).length,
    totalCharts: Object.keys(chartConfigs).length,
    availableTabsCount: availableTabs.value.length,
    activeTabId: activeTabId.value,
  }));

  // 初始化默认配置
  initializeDefaultTabs();

  return {
    // 状态（只读）
    tabConfigs: readonly(tabConfigs),
    chartConfigs: readonly(chartConfigs),
    availableTabs,
    activeTab,
    activeTabCharts,
    activeTabId: readonly(activeTabId),
    configStats,

    // 图表配置方法
    getAllTabs,
    getTabConfig,
    getChartConfig,
    getGroupIdByChartIdOrStatisticsId,
    getGroupConfig,
    updateChartConfig,
    addTabConfig,
    getTabCharts,
    resetConfigs,
    setActiveTab,
    getActiveTab,
    getActiveTabCharts,
    initializeActiveTab,

    // 🔥 新增：统计数据管理方法
    getStatisticsConfig,
    getCurrentTabStatistics,
    updateStatisticsData,

    // 🔥 新增：Tab级别的loading状态管理
    tabLoadingStates: readonly(tabLoadingStates),
    setTabLoading,
    getTabLoading,
    getCurrentTabLoading,
  };
}

/**
 * 🔥 修复：单例模式的Tab配置管理Hook
 * 确保所有组件共享相同的状态实例
 */
export function useTabConfigManager() {
  if (!globalTabConfigManager) {
    globalTabConfigManager = createTabConfigManager();
  }
  return globalTabConfigManager;
}

// ========== 上下文系统集成 ==========

/**
 * 🔥 新增：提供Tab配置上下文
 * 在顶层组件中调用，为子组件提供Tab配置
 */
export function provideTabConfigContext() {
  const tabConfigManager = useTabConfigManager();
  provide(TAB_CONFIG_CONTEXT_KEY, tabConfigManager);
  return tabConfigManager;
}

/**
 * 🔥 新增：使用Tab配置上下文（必需）
 * 在子组件中调用，获取Tab配置
 */
export function useTabConfigContext() {
  const context = inject(TAB_CONFIG_CONTEXT_KEY);
  if (!context) {
    throw new Error('useTabConfigContext must be used within a component that provides TabConfigContext');
  }
  return context;
}

/**
 * 🔥 新增：使用Tab配置上下文（可选）
 * 在子组件中调用，获取Tab配置（如果没有提供则返回null）
 */
export function useTabConfigContextOptional() {
  return inject(TAB_CONFIG_CONTEXT_KEY, null);
}
