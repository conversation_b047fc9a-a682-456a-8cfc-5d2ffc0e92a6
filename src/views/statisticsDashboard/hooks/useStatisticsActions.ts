/**
 * 统计数据上下文管理 Hook
 * 参照图表数据的上下文系统，为统计数据提供统一的上下文管理
 */

import { inject, provide, reactive, ref, type InjectionKey } from 'vue';
import type {
  StatisticsActionContext,
  StatisticsDataContext,
  StatisticsStateContext,
  StatisticsConfig,
  StatisticsItem,
} from '../types/statisticDashboard';
import { message } from 'ant-design-vue';
import { useTabConfigManager } from './useTabConfigManager';
import { useUnifiedDataLoader } from './useUnifiedDataLoader';

// 注入键
export const STATISTICS_ACTION_CONTEXT_KEY: InjectionKey<StatisticsActionContext> = Symbol('statisticsActionContext');
export const STATISTICS_DATA_CONTEXT_KEY: InjectionKey<StatisticsDataContext> = Symbol('statisticsDataContext');
export const STATISTICS_STATE_CONTEXT_KEY: InjectionKey<StatisticsStateContext> = Symbol('statisticsStateContext');

/**
 * 提供统计数据操作上下文（在顶层组件中使用）
 * 🔥 优化：内置状态管理，无需依赖外部状态上下文
 */
export function provideStatisticsActions() {
  // 使用配置管理器（延迟获取数据加载器，避免循环依赖）
  const tabConfigManager = useTabConfigManager();

  // 🔥 统计数据存储
  const statisticsDataStore = reactive<Record<string, StatisticsItem[]>>({});

  // 🔥 内置状态管理，不再依赖外部状态上下文
  const statisticsLoadingStates = reactive<Record<string, boolean>>({});
  const statisticsErrorStates = reactive<Record<string, string | null>>({});

  // 🔥 延迟获取数据加载器，避免循环依赖
  const getUnifiedLoader = (statisticsDataContext?: any) => {
    return useUnifiedDataLoader(statisticsDataContext);
  };

  // 🔥 优化：设置加载状态的辅助函数（使用内置状态管理）
  const setStatisticsLoadingState = (configId: string, loading: boolean) => {
    console.log(`🔥 setStatisticsLoadingState: ${configId} loading=${loading}`);

    // 设置内置状态
    statisticsLoadingStates[configId] = loading;

    console.log(`✅ 统计数据loading状态已设置: ${configId} = ${loading}`);
  };

  // 🔥 新增：设置错误状态的辅助函数
  const setStatisticsErrorState = (configId: string, error: string | null) => {
    statisticsErrorStates[configId] = error;
    console.log(`🔥 统计数据错误状态已设置: ${configId} = ${error}`);
  };

  /**
   * 刷新统计数据
   */
  const refreshStatistics = async (configId: string): Promise<void> => {
    try {
      setStatisticsLoadingState(configId, true);
      setStatisticsErrorState(configId, null);

      // 使用统一数据加载器刷新数据
      const unifiedLoader = getUnifiedLoader();
      const statisticsData = await unifiedLoader.refreshStatisticsData(configId);

      // 更新本地存储
      statisticsDataStore[configId] = statisticsData;

      setStatisticsLoadingState(configId, false);
      message.success(`统计数据已刷新: ${configId}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '刷新失败';
      setStatisticsLoadingState(configId, false);
      setStatisticsErrorState(configId, errorMessage);
      console.error(`❌ 刷新统计数据失败: ${configId}`, error);
      message.error(`刷新统计数据失败: ${errorMessage}`);
      throw error;
    }
  };

  /**
   * 重新加载所有统计数据
   */
  const reloadAllStatistics = async (): Promise<void> => {
    try {
      // 使用统一数据加载器批量加载
      const unifiedLoader = getUnifiedLoader();
      await unifiedLoader.loadAllAsyncStatistics();

      message.success('所有统计数据已重新加载');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '重新加载失败';
      console.error('❌ 重新加载所有统计数据失败', error);
      message.error(`重新加载失败: ${errorMessage}`);
      throw error;
    }
  };

  // 🔥 优化：创建包含状态管理的统一操作上下文对象
  const actionContext: StatisticsActionContext = {
    refreshStatistics,
    reloadAllStatistics,

    // 🔥 新增：内置状态管理
    statisticsLoadingStates,
    statisticsErrorStates,
    setStatisticsLoading: setStatisticsLoadingState,
    setStatisticsError: setStatisticsErrorState,
  };

  // 提供操作上下文
  provide(STATISTICS_ACTION_CONTEXT_KEY, actionContext);

  return {
    actionContext,
    statisticsDataStore,
    tabConfigManager,
    getUnifiedLoader, // 暴露延迟获取函数
    // 暴露状态管理函数
    setStatisticsLoadingState,
    setStatisticsErrorState,
  };
}

/**
 * 提供统计数据上下文
 */
export function provideStatisticsData(statisticsActionsReturn?: ReturnType<typeof provideStatisticsActions>) {
  /**
   * 获取统计数据
   */
  const getStatisticsData = (configId: string): StatisticsItem[] => {
    // 从统计数据存储获取
    if (statisticsActionsReturn?.statisticsDataStore[configId]) {
      return statisticsActionsReturn.statisticsDataStore[configId];
    }

    return [];
  };

  /**
   * 设置统计数据
   */
  const setStatisticsData = (configId: string, data: StatisticsItem[]) => {
    if (statisticsActionsReturn?.statisticsDataStore) {
      statisticsActionsReturn.statisticsDataStore[configId] = data;
    }

    if (statisticsActionsReturn?.tabConfigManager) {
      statisticsActionsReturn.tabConfigManager.updateStatisticsData(configId, data);
    }
  };

  /**
   * 刷新统计数据
   */
  const refreshStatisticsData = async (configId: string): Promise<void> => {
    if (statisticsActionsReturn?.actionContext.refreshStatistics) {
      await statisticsActionsReturn.actionContext.refreshStatistics(configId);
    } else {
      console.warn('refreshStatisticsData: 统计操作上下文不可用');
    }
  };

  /**
   * 清除统计数据缓存
   */
  const clearStatisticsCache = () => {
    if (statisticsActionsReturn?.statisticsDataStore) {
      Object.keys(statisticsActionsReturn.statisticsDataStore).forEach((key) => {
        delete statisticsActionsReturn.statisticsDataStore[key];
      });
    }
  };

  /**
   * 获取统计配置
   */
  const getStatisticsConfig = (configId: string): StatisticsConfig | null => {
    if (statisticsActionsReturn?.tabConfigManager) {
      return statisticsActionsReturn.tabConfigManager.getStatisticsConfig(configId);
    }
    return null;
  };

  const dataContext: StatisticsDataContext = {
    getStatisticsData,
    setStatisticsData,
    refreshStatisticsData,
    clearStatisticsCache,
    getStatisticsConfig,
  };

  provide(STATISTICS_DATA_CONTEXT_KEY, dataContext);

  return dataContext;
}

/**
 * 提供统计数据状态上下文
 */
export function provideStatisticsState() {
  // 状态管理
  const loading = ref(false);
  const statisticsLoadingStates = reactive<Record<string, boolean>>({});
  const statisticsErrorStates = reactive<Record<string, string | null>>({});

  /**
   * 设置统计配置loading状态
   */
  const setStatisticsLoading = (configId: string, loading: boolean) => {
    statisticsLoadingStates[configId] = loading;
  };

  /**
   * 设置统计数据错误状态
   */
  const setStatisticsError = (configId: string, error: string | null) => {
    statisticsErrorStates[configId] = error;
  };

  const stateContext: StatisticsStateContext = {
    loading: loading.value,
    statisticsLoadingStates,
    setStatisticsLoading,
    statisticsErrorStates,
    setStatisticsError,
  };

  provide(STATISTICS_STATE_CONTEXT_KEY, stateContext);

  return {
    stateContext,
    loading,
    statisticsLoadingStates,
    statisticsErrorStates,
  };
}

// ========== 使用函数 ==========

/**
 * 使用统计数据操作上下文
 */
export function useStatisticsActions() {
  const context = inject(STATISTICS_ACTION_CONTEXT_KEY);

  if (!context) {
    throw new Error('useStatisticsActions must be used within a component that provides StatisticsActionContext');
  }

  return context;
}

/**
 * 使用统计数据操作上下文（可选版本）
 */
export function useStatisticsActionsOptional() {
  const context = inject(STATISTICS_ACTION_CONTEXT_KEY);

  if (!context) {
    console.warn('StatisticsActionContext not provided, using default implementation');
    // 🔥 优化：返回包含状态管理的默认实现
    return {
      refreshStatistics: async (configId: string) => {
        console.warn('StatisticsActionContext not provided, using default implementation');
        message.info(`刷新统计数据 (默认实现): ${configId}`);
      },
      reloadAllStatistics: async () => {
        console.warn('StatisticsActionContext not provided, using default implementation');
        message.info('重新加载所有统计数据 (默认实现)');
      },
      // 🔥 新增：默认状态管理
      statisticsLoadingStates: {},
      statisticsErrorStates: {},
      setStatisticsLoading: (_configId: string, _loading: boolean) => {
        console.warn('StatisticsActionContext not provided, cannot set loading state');
      },
      setStatisticsError: (_configId: string, _error: string | null) => {
        console.warn('StatisticsActionContext not provided, cannot set error state');
      },
    };
  }

  return context;
}

/**
 * 使用统计数据上下文
 */
export function useStatisticsData() {
  const context = inject(STATISTICS_DATA_CONTEXT_KEY);

  if (!context) {
    throw new Error('useStatisticsData must be used within a component that provides StatisticsDataContext');
  }

  return context;
}

/**
 * 使用统计数据上下文（可选版本）
 */
export function useStatisticsDataOptional() {
  const context = inject(STATISTICS_DATA_CONTEXT_KEY);

  if (!context) {
    console.warn('StatisticsDataContext not provided, using default implementation');
    // 返回默认实现
    return {
      getStatisticsData: (_configId: string): StatisticsItem[] => {
        console.warn('StatisticsDataContext not provided, returning empty array');
        return [];
      },
      setStatisticsData: (_configId: string, _data: StatisticsItem[]) => {
        console.warn('StatisticsDataContext not provided, cannot set data');
      },
      refreshStatisticsData: async (configId: string) => {
        console.warn('StatisticsDataContext not provided, using default implementation');
        message.info(`刷新统计数据 (默认实现): ${configId}`);
      },
      clearStatisticsCache: () => {
        console.warn('StatisticsDataContext not provided, cannot clear cache');
      },
      getStatisticsConfig: (_configId: string): StatisticsConfig | null => {
        console.warn('StatisticsDataContext not provided, returning null');
        return null;
      },
    };
  }

  return context;
}

/**
 * 使用统计数据状态上下文
 */
export function useStatisticsState() {
  const context = inject(STATISTICS_STATE_CONTEXT_KEY);

  if (!context) {
    throw new Error('useStatisticsState must be used within a component that provides StatisticsStateContext');
  }

  return context;
}

/**
 * 使用统计数据状态上下文（可选版本）
 */
export function useStatisticsStateOptional() {
  const context = inject(STATISTICS_STATE_CONTEXT_KEY);

  if (!context) {
    console.warn('StatisticsStateContext not provided, using default implementation');
    // 返回默认实现
    return {
      loading: false,
      statisticsLoadingStates: {},
      setStatisticsLoading: (_configId: string, _loading: boolean) => {
        console.warn('StatisticsStateContext not provided, cannot set loading state');
      },
      statisticsErrorStates: {},
      setStatisticsError: (_configId: string, _error: string | null) => {
        console.warn('StatisticsStateContext not provided, cannot set error state');
      },
    };
  }

  return context;
}
