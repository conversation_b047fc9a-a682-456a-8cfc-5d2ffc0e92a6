/**
 * 筛选器管理 Hook
 * 提供筛选条件的管理、验证和应用功能
 */

import { ref, computed, readonly, reactive, onMounted, nextTick, Ref, watch } from 'vue';
import { FormSchema } from '/@/components/Table';
import { useI18n } from '/@/hooks/web/useI18n';
import dayjs from 'dayjs';
import { useUserStore } from '/@/store/modules/user';
import { useForm } from '/@/components/Form';
import { awaitTo } from '@ruqi/utils-admin';
import { queryRegionDetailsApi } from '../../system/depart/depart.api';
import { queryAreaHaveAuth } from '/@/api/common/api';
import { StatisticalDimensionTypeEnum } from '../enums';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import isoWeek from 'dayjs/plugin/isoWeek';
import { AccountTypeEnum } from '/@/enums/common';
import { useTabConfigManager } from './useTabConfigManager';

// 扩展dayjs插件
dayjs.extend(weekOfYear);
dayjs.extend(quarterOfYear);
dayjs.extend(isoWeek);

/**
 * 统计周期配置接口
 */
interface StatPeriodConfig {
  displayFormat: string;
  pickerType: string | undefined;
  defaultRange: {
    subtract: number;
    unit: string;
  };
}

/**
 * 筛选器管理
 */
export function useFilters(opt: { enableUserDataAuth: boolean; type?: 'global' | 'local' }) {
  // type 表示区分全局筛选器和局部筛选器类型
  const { enableUserDataAuth, type = 'global' } = opt;

  const { t } = useI18n('common');

  const { userInfo } = useUserStore();

  // 初始化状态管理
  let initializationResolve: ((value: any) => void) | null = null;
  const initializationPromise = new Promise((resolve) => {
    initializationResolve = resolve;
  });

  const isInitialized = ref(false);

  // 统一的输出格式配置
  const OUTPUT_FORMAT_CONFIG = {
    // 可选格式类型
    DATETIME: 'YYYY-MM-DD HH:mm:ss',
    DATE: 'YYYY-MM-DD',
    TIMESTAMP: 'timestamp', // 时间戳格式(秒)
  };

  // 显示格式配置
  const DISPLAY_FORMAT_CONFIG = {
    UNIFIED: 'YYYY-MM-DD', // 统一显示格式
    ADAPTIVE: 'adaptive', // 自适应显示格式（根据统计周期变化）
  };

  // 当前使用的输出格式（可配置）
  const outputFormat = ref<string>(OUTPUT_FORMAT_CONFIG.DATE);

  // 当前使用的显示格式模式（可配置）
  const displayFormatMode = ref<string>(DISPLAY_FORMAT_CONFIG.ADAPTIVE);

  // 统计周期配置映射-全局筛选器和局部筛选器的默认配置
  const configTypeMap = {
    global: {
      currentStatPeriod: 'day',
      defaultMonth: 6,
      config: {},
    },
    local: {
      currentStatPeriod: 'month',
      defaultMonth: 2, // 3个月，这里需要减1
      config: {},
    },
  };

  // 统计周期配置映射-全局筛选器
  const GLOBAL_STAT_PERIOD_CONFIG: Record<string, StatPeriodConfig> = {
    day: {
      displayFormat: 'YYYY-MM-DD',
      pickerType: undefined, // 默认日期选择
      defaultRange: { subtract: 6, unit: 'day' }, // 近7天
    },
    week: {
      displayFormat: 'YYYY-wo',
      pickerType: 'week',
      defaultRange: { subtract: 3, unit: 'week' }, // 近4周
    },
    month: {
      displayFormat: 'YYYY-MM',
      pickerType: 'month',
      defaultRange: { subtract: configTypeMap[type].defaultMonth, unit: 'month' }, // 全局筛选器近6个月，局部筛选器近3个月
    },
    season: {
      displayFormat: 'YYYY-[Q]Q',
      pickerType: 'quarter',
      defaultRange: { subtract: 30, unit: 'month' }, // 近10个季度 (每个季度3个月)
    },
    year: {
      displayFormat: 'YYYY',
      pickerType: 'year',
      defaultRange: { subtract: 4, unit: 'year' }, // 近5年
    },
  };

  // 统计周期配置映射-局部筛选器，不含天、周
  const { day: _day, week: _week, ...LOCAL_STAT_PERIOD_CONFIG } = GLOBAL_STAT_PERIOD_CONFIG;

  // 当前选择的统计周期
  const currentStatPeriod = ref<string>(configTypeMap[type].currentStatPeriod);

  configTypeMap.global.config = GLOBAL_STAT_PERIOD_CONFIG;
  configTypeMap.local.config = LOCAL_STAT_PERIOD_CONFIG;

  /**
   * 根据统计周期调整日期范围到对应周期的开始和结束
   * @param startDate 开始日期
   * @param endDate 结束日期
   * @param period 统计周期
   * @returns 调整后的日期范围
   */
  function adjustDateRangeByPeriod(startDate: string, endDate: string, period: string) {
    const start = dayjs(startDate);
    const end = dayjs(endDate);
    const today = dayjs();

    switch (period) {
      case 'week':
        // 周统计：调整到周的开始和结束（周一到周日）
        const adjustedStartWeek = start.startOf('isoWeek');
        let adjustedEndWeek = end.endOf('isoWeek');

        // 如果结束周的最后一天超过了当天，调整为今天
        if (adjustedEndWeek.isAfter(today, 'day')) {
          adjustedEndWeek = today;
        }

        return {
          startDate: adjustedStartWeek.format('YYYY-MM-DD'),
          endDate: adjustedEndWeek.format('YYYY-MM-DD'),
        };

      case 'month':
        // 月统计：调整到月的开始和结束
        const adjustedStartMonth = start.startOf('month');
        let adjustedEndMonth = end.endOf('month');

        // 如果结束月的最后一天超过了当天，调整为今天
        if (adjustedEndMonth.isAfter(today, 'day')) {
          adjustedEndMonth = today;
        }

        return {
          startDate: adjustedStartMonth.format('YYYY-MM-DD'),
          endDate: adjustedEndMonth.format('YYYY-MM-DD'),
        };

      case 'season':
        // 季度统计：调整到季度的开始和结束
        const adjustedStartQuarter = start.startOf('quarter');
        let adjustedEndQuarter = end.endOf('quarter');

        // 如果结束季度的最后一天超过了当天，调整为今天
        if (adjustedEndQuarter.isAfter(today, 'day')) {
          adjustedEndQuarter = today;
        }

        return {
          startDate: adjustedStartQuarter.format('YYYY-MM-DD'),
          endDate: adjustedEndQuarter.format('YYYY-MM-DD'),
        };

      case 'year':
        // 年统计：调整到年的开始和结束
        const adjustedStartYear = start.startOf('year');
        let adjustedEndYear = end.endOf('year');

        // 如果结束年的最后一天超过了当天，调整为今天
        if (adjustedEndYear.isAfter(today, 'day')) {
          adjustedEndYear = today;
        }

        return {
          startDate: adjustedStartYear.format('YYYY-MM-DD'),
          endDate: adjustedEndYear.format('YYYY-MM-DD'),
        };

      case 'day':
      default:
        // 天统计：不需要调整，直接返回
        return {
          startDate: start.format('YYYY-MM-DD'),
          endDate: end.format('YYYY-MM-DD'),
        };
    }
  }

  const formSchemas: Ref<FormSchema[]> = ref([
    {
      field: 'statPeriod',
      label: t('statPeriod'),
      component: 'Select',
      labelWidth: '100%',
      required: true,
      componentProps: {
        placeholder: t('chooseText'),
        options: [
          { label: t('time_days'), value: 'day' },
          { label: t('_week'), value: 'week' },
          { label: t('month'), value: 'month' },
          { label: t('quarter'), value: 'season' },
          { label: t('year'), value: 'year' },
        ],
        allowClear: false,
      },
      defaultValue: configTypeMap[type].currentStatPeriod,
    },
    {
      field: 'periodRange',
      label: t('periodRange'),
      component: 'RangePicker',
      labelWidth: '100%',
      colProps: { span: 6 },
      required: true,
      componentProps: {
        ...getPickerConfig(currentStatPeriod.value),
        // picker: initPickerConfig.picker,
        // format: initPickerConfig.format, // 显示格式
        // valueFormat: initPickerConfig.valueFormat, // 提交格式（统一格式）
        valueType: 'array', // 关键配置：保持数组格式，不转换为字符串
        // disabledDate: initPickerConfig.disabledDate, // 添加日期禁用逻辑
        allowClear: false,
      },
      defaultValue: getPeriodRangeDefaultValue(currentStatPeriod.value),
    },
    {
      field: 'regionCode',
      label: t('region_text'),
      component: 'Select',
      labelWidth: '100%',
      required: false,
      componentProps: {
        placeholder: t('chooseText'),
        options: [],
        showSearch: true,
        fieldNames: {
          label: 'regionName',
          value: 'regionCode',
        },
      },
      ifShow: true,
    },
    {
      field: 'countryCode',
      label: t('country'),
      component: 'Select',
      labelWidth: '100%',
      required: false,
      componentProps: {
        placeholder: t('chooseText'),
        options: [],
        showSearch: true,
        fieldNames: {
          label: 'countryName',
          value: 'countryCode',
        },
      },
      ifShow: true,
    },
    {
      field: 'provinceCode',
      label: t('provinceName'),
      component: 'Select',
      labelWidth: '100%',
      required: false,
      componentProps: {
        placeholder: t('chooseText'),
        options: [],
        showSearch: true,
        fieldNames: {
          label: 'provinceName',
          value: 'provinceCode',
        },
      },
      ifShow: false,
    },
    {
      field: 'cityCode',
      label: t('city'),
      component: 'Select',
      labelWidth: '100%',
      required: false,
      componentProps: {
        placeholder: t('chooseText'),
        options: [],
        showSearch: true,
        fieldNames: {
          label: 'cityName',
          value: 'cityCode',
        },
      },
      ifShow: false,
    },
    {
      field: 'dealerCode', // 同时包含B端用户和网点
      label: '',
      component: 'Select',
      labelWidth: '100%',
      required: false,
      componentProps: {
        placeholder: t('chooseText'),
        options: [],
        showSearch: true,
        fieldNames: {
          label: 'networkName',
          value: 'networkCode',
        },
      },
      ifShow: false,
    },
  ]);

  /**
   * 根据统计周期计算默认的时间范围
   */
  function getDefaultDateRange(statPeriod: string) {
    const now = dayjs();
    const config = configTypeMap[type].config[statPeriod] || GLOBAL_STAT_PERIOD_CONFIG[statPeriod];
    const { subtract, unit } = config.defaultRange;
    console.log('config.defaultRange', config.defaultRange);
    return [now.subtract(subtract, unit as any).format(OUTPUT_FORMAT_CONFIG.DATE), now.format(OUTPUT_FORMAT_CONFIG.DATE)];
  }

  /**
   * 获取periodRange的默认值
   */
  function getPeriodRangeDefaultValue(statPeriod: string) {
    return getDefaultDateRange(statPeriod);
  }

  /** 所有基础数据(大区、国家、州/省、城市、门店、网点(网点和B端用户共用一个下拉列表)) */

  const baseData = reactive<Record<string, Record<string, string>[]>>({
    regionOptions: [],
    countryOptions: [],
    provinceOptions: [],
    cityOptions: [],
    networkOptions: [], // 包含B端用户和网点
  });

  const { getTabLoading, activeTabId } = useTabConfigManager();

  // ========== Tab模式切换状态管理 ==========

  /**
   * 字段状态接口
   */
  interface FieldState {
    ifShow: boolean;
    componentProps?: any;
  }

  // 原始字段状态（权限处理完成后的状态）
  const originalFieldStates = ref<Record<string, FieldState>>({});

  // 是否处于用户统计模式
  const isUserStatisticsMode = ref(false);

  /**
   * 保存原始字段状态
   * 在权限处理完成后调用，保存字段的原始显示状态
   */
  const saveOriginalFieldStates = () => {
    const fieldsToSave = ['provinceCode', 'cityCode', 'dealerCode'];

    fieldsToSave.forEach((fieldName) => {
      const schema = formSchemas.value.find((s) => s.field === fieldName);
      if (schema) {
        originalFieldStates.value[fieldName] = {
          ifShow: schema.ifShow || false,
          componentProps: { ...schema.componentProps },
        };
      }
    });

    console.log('📋 保存原始字段状态:', originalFieldStates.value);
  };

  /**
   * 应用用户统计模式
   * 隐藏省份、城市、经销商字段，只保留到国家层级
   */
  const applyUserStatisticsMode = () => {
    if (isUserStatisticsMode.value) return; // 避免重复应用

    console.log('🔄 切换到用户统计模式，隐藏省份/城市/经销商字段');

    // 隐藏省份、城市、经销商字段
    const fieldsToHide = ['provinceCode', 'cityCode', 'dealerCode'];

    fieldsToHide.forEach((fieldName) => {
      formMethods?.updateSchema({
        field: fieldName,
        ifShow: false,
      });
    });

    isUserStatisticsMode.value = true;
  };

  /**
   * 恢复原始模式
   * 根据保存的原始状态恢复字段显示
   */
  const restoreOriginalMode = () => {
    if (!isUserStatisticsMode.value) return; // 避免重复恢复

    console.log('🔄 恢复原始模式，根据权限显示字段');

    // 根据原始状态恢复字段显示
    Object.keys(originalFieldStates.value).forEach((fieldName) => {
      const originalState = originalFieldStates.value[fieldName];
      if (originalState) {
        formMethods?.updateSchema({
          field: fieldName,
          ifShow: originalState.ifShow,
          componentProps: originalState.componentProps,
        });
      }
    });

    isUserStatisticsMode.value = false;
  };

  /**
   * 处理Tab模式切换
   */
  const handleTabModeChange = (tabId: string) => {
    // 确保初始化完成且有原始状态数据
    if (!isInitialized.value || Object.keys(originalFieldStates.value).length === 0) {
      return;
    }

    if (tabId === 'userStatistics') {
      applyUserStatisticsMode();
    } else {
      restoreOriginalMode();
    }
  };

  const submitButtonOptions = computed(() => ({
    loading: getTabLoading(activeTabId.value),
  }));

  const [registerForm, formMethods] = useForm({
    schemas: formSchemas,
    showActionButtonGroup: true,
    showSubmitButton: true,
    showResetButton: true,
    submitButtonOptions: submitButtonOptions,
    layout: 'vertical',
    labelAlign: 'left',
    // 设置按钮布局到右边
    buttonLayout: 'right',
    // 设置默认五列布局
    baseColProps: {
      span: 4, // 24 / 6 = 4，实现五列布局（考虑到时间范围占用6列）
      xs: 24, // 超小屏幕一列
      sm: 12, // 小屏幕两列
      md: 8, // 中等屏幕三列
      lg: 6, // 大屏幕四列
      xl: 4, // 超大屏幕五列
      xxl: 4, // 超超大屏幕五列
    },
    // 启用折叠展开功能
    showAdvancedButton: true,
    // 超过5个字段时自动折叠
    autoAdvancedCol: 5,
    alwaysShowLines: 1,
  });

  const filters = computed(() => formMethods?.getFieldsValue() ?? {});

  /**
   * 根据统计周期获取picker配置
   */
  function getPickerConfig(statPeriod: string) {
    const config = configTypeMap[type].config[statPeriod] || GLOBAL_STAT_PERIOD_CONFIG.day;
    // 显示格式（用户看到的格式）
    const getDisplayFormat = () => {
      if (displayFormatMode.value === DISPLAY_FORMAT_CONFIG.UNIFIED) {
        return DISPLAY_FORMAT_CONFIG.UNIFIED; // 其他统计周期统一使用 YYYY-MM-DD
      }
      return config.displayFormat; // 自适应格式（根据统计周期变化）
    };

    // 值格式（提交给后端的格式）
    const getValueFormat = () => {
      if (outputFormat.value === OUTPUT_FORMAT_CONFIG.TIMESTAMP) {
        return 'X'; // dayjs的时间戳格式
      }
      return outputFormat.value; // 统一的日期时间格式
    };

    return {
      picker: config.pickerType,
      format: getDisplayFormat(),
      valueFormat: getValueFormat(),
      disabledDate: (current: any) => {
        const now = dayjs();
        // 禁用未来的日期
        if (current.isAfter(now, 'day')) {
          return true;
        }
      }, // 添加日期禁用逻辑
    };
  }

  /**
   * 设置输出格式
   * @param format 输出格式类型
   */
  const setOutputFormat = (format: string) => {
    outputFormat.value = format;
  };

  /**
   * 获取当前输出格式
   */
  const getOutputFormat = () => {
    return outputFormat.value;
  };

  /**
   * 设置显示格式模式
   * @param mode 显示格式模式
   */
  const setDisplayFormatMode = (mode: string) => {
    displayFormatMode.value = mode;
  };

  /**
   * 获取当前显示格式模式
   */
  const getDisplayFormatMode = () => {
    return displayFormatMode.value;
  };

  const handleUserDataAuth = async () => {
    const [_err, result] = await awaitTo(Promise.all([queryAreaHaveAuth(userInfo!.userId as string), queryRegionDetailsApi()]));
    const [authData = {}, allBaseData] = result || [];
    console.log('userInfo', userInfo);
    console.log('allBaseData', allBaseData);
    // 1. 先对大区和国家数据进行筛选过滤出已有权限的数据。
    // 2. 判断用户的数据权限里是否存在大区且只有一条数据，如果有直接赋值，然后更新国家数据源
    // 3. 判断用户的数据权限里是否存在国家且只有一条数据，如果有直接赋值
    // 4. 判断账户类型，如果是厂端，判断用户的数据权限里是否存在州/省且只有一条数据，如果有直接赋值，更新州/省/数据源
    // 5. 判断用户的数据权限里是否存在城市且只有一条数据，如果有直接赋值，然后更新城市数据源
    // 6. 门店判断 todo
    // 7. 如果是总代&子公司，经销商判断 todo
    // 8. 如果是店端，门店判断 todo
    // 9. 每个层级的需要有数据联动
    /**
     * 1. 获取当前登陆用户的各数据权限集合（大区、国家/地区、州/省、城市）。
     * cityAuthList 用户城市权限数据
     * countryAuthList 用户国家权限数据
     * provinceAuthList 用户州/省权限数据
     * regionCode 用户大区权限数据, 只有一个
     */
    const { regionCode = '', accountCategory, networkList } = userInfo ?? {};
    const { cityAuthList = [], countryAuthList = [], provinceAuthList = [] } = authData;

    console.log('authData', authData);
    const params = {} as Record<string, string | number>;

    // 2. 对大区和国家数据进行过滤筛选出有权限的数据
    allBaseData?.forEach((item: Record<string, any>) => {
      const { regionName, regionCode: _regionCode, countries } = item ?? {};

      if (regionCode === _regionCode) {
        // 大区数据
        baseData.regionOptions.push({ regionName, regionCode: _regionCode });

        countries?.forEach((country: Record<string, any>) => {
          const { countryCode, countryName, provinces, hasProvince, cities } = country ?? {};
          if (countryAuthList?.includes(countryCode)) {
            baseData.countryOptions.push({ countryCode, countryName, regionCode: _regionCode, hasProvince });
          }
          // 非厂端账户只处理到国家/地区层级
          if (accountCategory !== AccountTypeEnum.FACTORY_END) {
            return;
          }
          if (hasProvince && provinces?.length) {
            provinces.forEach((province: Record<string, any>) => {
              const { provinceName, provinceCode } = province ?? {};
              if (provinceAuthList?.includes(provinceCode)) {
                baseData.provinceOptions.push({
                  provinceCode,
                  provinceName,
                  countryCode,
                });
                // 如果州/省下存在城市数据，继续处理
                if (province.cities?.length) {
                  province.cities.forEach((city: Record<string, any>) => {
                    const { cityName, cityCode } = city ?? {};
                    if (cityAuthList?.includes(cityCode)) {
                      baseData.cityOptions.push({
                        cityCode,
                        cityName,
                        provinceCode,
                      });
                    }
                  });
                }
              }
            });
          } else {
            cities?.forEach((city: Record<string, any>) => {
              const { cityCode, cityName } = city ?? {};
              if (cityAuthList?.includes(cityCode)) {
                baseData.cityOptions.push({
                  cityCode,
                  cityName,
                  countryCode,
                });
              }
            });
          }
        });
      }
    });

    // 2. 1 处理网点&B端用户数据
    if (networkList?.length) {
      networkList.forEach((network: Record<string, any>) => {
        const { networkCode, networkName } = network ?? {};
        baseData.networkOptions.push({
          networkCode,
          networkName,
        });
      });
    }
    // 3. 更新数据源
    // 3.1 更新大区数据源
    formMethods.updateSchema([
      {
        field: 'regionCode',
        componentProps: {
          options: baseData.regionOptions,
        },
      },
    ]);

    // 3.2 赋值大区并更新国家数据源
    params.regionCode = regionCode;
    formMethods.updateSchema({
      field: 'countryCode',
      componentProps: {
        options: baseData.countryOptions,
      },
    });

    console.log('params', params);

    // 3.3 赋值国家 当前大区下的国家只有一条数据的时候
    if (countryAuthList?.length === 1) {
      const countryCode = countryAuthList[0];
      params.countryCode = countryCode;
      // 非厂端账户
      if ([AccountTypeEnum.NETWORK, AccountTypeEnum.GENERAL_AGENT_SUBSIDIARY].includes(accountCategory as AccountTypeEnum)) {
        const isNetwork = accountCategory === AccountTypeEnum.NETWORK;
        formMethods?.updateSchema({
          field: 'dealerCode',
          label: isNetwork ? t('network_text') : t('bSideUsers'),
          componentProps: {
            options: baseData.networkOptions,
          },
          ifShow: true,
        });
        if (baseData.networkOptions.length === 1) {
          params.dealerCode = baseData.networkOptions[0].networkCode;
        }
      } else {
        // 厂端账户
        const { hasProvince } = baseData.countryOptions.find((country) => country.countryCode === countryCode) ?? {};
        if (hasProvince) {
          // 3.4 更新州/省 数据源并确保字段显示
          formMethods.updateSchema({
            field: 'provinceCode',
            ifShow: baseData.provinceOptions.length > 0,
            componentProps: {
              options: baseData.provinceOptions,
            },
          });
          // 3.5 赋值州/省数据，当前国家下只有一条州/省数据的时候,provinceAuthList是全部的州/省数据
          const provinceCodes = baseData.provinceOptions
            .filter((province) => province.countryCode === countryCode)
            .map((item) => item.provinceCode)
            .filter((item) => provinceAuthList.includes(item));
          if (provinceCodes.length === 1) {
            const provinceCode = provinceCodes[0];
            params.provinceCode = provinceCode;
            // 3.6 更新城市 数据源并确保字段显示
            formMethods.updateSchema({
              field: 'cityCode',
              ifShow: baseData.cityOptions.length > 0,
              componentProps: {
                options: baseData.cityOptions,
              },
            });
            // 3.7 赋值城市数据，当前州/省下城市只有一条数据的时候
            const cityCodes = baseData.cityOptions
              .filter((city) => city.provinceCode === provinceCode)
              .map((item) => item.cityCode)
              .filter((item) => cityAuthList.includes(item));
            if (cityCodes.length === 1) {
              const cityCode = cityCodes[0];
              params.cityCode = cityCode;
            }
          }
        } else {
          // 3.8 更新城市 数据源（无省份的国家）
          formMethods.updateSchema({
            field: 'cityCode',
            ifShow: baseData.cityOptions.length > 0,
          });
        }
      }
    }
    console.log('provinceAuthList', provinceAuthList);
    // 赋值州/省，当前国家/地区下的州/省只有一条数据的时候
    // if (provinceAuthList?.length === 1) {
    //   const provinceCode = provinceAuthList[0];
    //   params.provinceCode = provinceCode;
    //   formMethods.updateSchema({
    //     field: 'cityCode',
    //     componentProps: {
    //       options: baseData.cityOptions,
    //     },
    //     ifShow: true
    //   });
    // }

    // 赋值城市，当前州/省下城市只有一条数据的时候
    // if (cityAuthList?.length === 1) {
    //   const cityCode = cityAuthList[0];
    //   params.cityCode = cityCode;
    // }

    // 设置表单值
    formMethods?.setFieldsValue(params);

    // 等待确保表单值设置完成
    await nextTick();

    // 标记初始化完成
    isInitialized.value = true;

    // 🔥 新增：保存原始字段状态，用于Tab切换时的模式切换
    saveOriginalFieldStates();

    if (initializationResolve) {
      initializationResolve(params);
    }
  };

  const handleFieldChange = async (field: string, value: string) => {
    const fieldValues = formMethods.getFieldsValue() ?? {};

    // 当统计周期变化时，更新时间范围选择器的配置和默认值
    if (field === 'statPeriod' && value) {
      // 更新当前统计周期
      currentStatPeriod.value = value;
      const pickerConfig = getPickerConfig(value);
      formMethods?.updateSchema({
        field: 'periodRange',
        componentProps: {
          ...pickerConfig,
          valueType: 'array', // 关键配置：保持数组格式，不转换为字符串
          allowClear: false,
        },
      });
      await nextTick();
      const defaultRange = getPeriodRangeDefaultValue(value);
      // 设置默认值
      formMethods?.setFieldsValue({
        periodRange: defaultRange,
      });
      return;
    }

    if (field === 'regionCode') {
      // 更新国家选项
      const countryOptions = !value ? [] : baseData.countryOptions.filter((country) => country.regionCode === value);
      formMethods?.updateSchema({
        field: 'countryCode',
        componentProps: {
          options: countryOptions,
        },
      });
      !value &&
        formMethods.setFieldsValue({
          countryCode: undefined,
        });
      return;
    }

    if (field === 'countryCode') {
      const countryCode = value;
      const findCountryValue = baseData.countryOptions.find((country) => country.countryCode === countryCode) ?? {};
      const { hasProvince } = findCountryValue;
      if (hasProvince) {
        // 更新省份选项
        const provinceOptions = !value ? [] : baseData.provinceOptions.filter((province) => province.countryCode === countryCode);
        formMethods.updateSchema({
          field: 'provinceCode',
          ifShow: provinceOptions.length > 0,
          componentProps: {
            options: provinceOptions,
          },
        });
      } else {
        // 更新城市选项
        const cityOptions = !value ? [] : baseData.cityOptions.filter((city) => city.countryCode === countryCode);
        formMethods.updateSchema({
          field: 'cityCode',
          ifShow: cityOptions.length > 0,
          componentProps: {
            options: cityOptions,
          },
        });
        !value &&
          formMethods.setFieldsValue({
            cityCode: '',
          });
      }
      return;
    }

    if (field === 'provinceCode') {
      // 更新城市选项
      const cityOptions = !value ? [] : baseData.cityOptions.filter((city) => city.provinceCode === value);
      formMethods.updateSchema({
        field: 'cityCode',
        ifShow: cityOptions.length > 0,
      });
      fieldValues.cityCode &&
        formMethods.setFieldsValue({
          cityCode: undefined,
        });
      return;
    }
  };

  // ========== Tab模式切换监听 ==========

  /**
   * 监听Tab变化，处理用户统计模式切换
   */
  watch(
    () => activeTabId.value,
    (newTabId) => {
      console.log('📋 Tab切换:', newTabId);
      handleTabModeChange(newTabId);
    },
    { immediate: false } // 不立即执行，等待初始化完成
  );

  onMounted(() => {
    if (enableUserDataAuth) {
      // 启用用户数据权限
      handleUserDataAuth();
    } else {
      // 如果未启用用户数据权限，立即标记为初始化完成
      isInitialized.value = true;
      if (initializationResolve) {
        initializationResolve(null);
      }
    }
  });

  /**
   * 获取默认的查询参数（用于初始加载）
   */
  const getDefaultQueryParams = () => {
    const defaultRange = getDefaultDateRange(currentStatPeriod.value);

    // 🔧 修复：强制使用日期格式，避免时分秒格式导致的不一致
    return {
      startDate: defaultRange[0],
      endDate: defaultRange[1],
      statisticalDimension: 'DAY' as StatisticalDimensionTypeEnum,
      regionCenterCode: userInfo?.regionCenterCode,
    };
  };

  // watch(
  //   () => activeTabId.value,
  //   (newVal) => {
  //     // 如果当前Tab是用户统计模块，筛选条件只到国家层
  //     if (newVal === 'userStatistics') console.log('activeTabId', newVal);
  //   }
  // );

  return {
    currentStatPeriod,
    filters,

    // 配置项
    formSchemas,
    OUTPUT_FORMAT_CONFIG,
    DISPLAY_FORMAT_CONFIG,
    submitButtonOptions,

    // 表单配置
    registerForm,
    formMethods,

    // 方法
    getPeriodRangeDefaultValue,
    getDefaultDateRange,
    getPickerConfig,
    setOutputFormat,
    getOutputFormat,
    setDisplayFormatMode,
    getDisplayFormatMode,
    handleFieldChange,

    // 新增方法
    getDefaultQueryParams,
    adjustDateRangeByPeriod,

    // 初始化相关
    initializationPromise,
    isInitialized: readonly(isInitialized),
  };
}
