<template>
  <PageWrapper>
    <div :style="{ height: `${screenHeight}px`, backgroundImage: `url(${workbenches})` }" class="bg-cover bg-center">
      <div class="flex justify-between items-center b-b-1 h-60px bg-white">
        <div class="flex items-center">
          <img :src="logoImg" class="app-loading-logo h-40px" alt="Logo" />
          <div class="text-16px font-bold">{{ t('common.managementPortal') }}</div>
        </div>
        <div class="flex items-center">
          <AppLocalePicker v-if="getShowLocalePicker" :reload="true" :showText="true" :class="`${prefixCls}-action__item`" />
          <UserDropDown :theme="getHeaderTheme" />
        </div>
      </div>
      <div class="pl-2.5% pt-2.5%">
        <div class="">
          <div class="workbenchesTitle">{{ t('common.workbenchesTitle') }}</div>
          <div class="workbenchesDesc">{{ t('common.workbenchesDesc') }}</div>
        </div>
        <div class="w-full enter-y mt-10px">
          <div class="groupTitle">{{ t('common.userOperations_GF') }}</div>
          <div class="every_platform" v-for="items in platformData" :key="items.clientId">
            <div class="content h-134px">
              <div class="title">{{ items.name }}</div>
              <div class="select" style="height: 36px">
                <a-select
                  v-if="['2', '3'].includes(items.clientId)"
                  style="width: 100%; height: 36px"
                  :placeholder="t('common.plase_login_site_text')"
                  v-model:value="items.selectSite"
                >
                  <a-select-option v-for="item in items.options" :disabled="item.disabled" :key="item.url">{{ item.region_country }}</a-select-option>
                </a-select>
              </div>
            </div>
            <div class="goto" @click.stop="goToLink(items)">
              {{ t('common.unlockEntry') }}
              <right-outlined class="rightIcon" />
            </div>
          </div>
        </div>
        <div class="w-full enter-y mt-10px">
          <div v-auth="AuthEnum.REGIONAL_OPERATIONS_USER_STATISTICS_DASHBOARD">
            <div class="groupTitle">{{ t('common.dataStatisticsAndVisualization') }}</div>
            <div class="every_platform">
              <div class="content h-134px">
                <div class="title">
                  {{ t('common.regionalOperationsUserStatisticsDashboard') }}
                </div>
              </div>
              <div class="goto" @click.stop="goToLink(null, '/statistics-dashboard/regional-operations-user-statistics')">
                {{ t('common.unlockEntry') }}
                <right-outlined class="rightIcon" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageWrapper>
  <UpdatePassword v-if="passwordVisible" ref="updatePasswordRef" />
</template>
<script lang="ts" setup>
  import { RightOutlined } from '@ant-design/icons-vue';
  import { ref, onMounted } from 'vue';
  import { message } from 'ant-design-vue';
  import { PageWrapper } from '/@/components/Page';
  import { usePermissionStore } from '/@/store/modules/permission';
  import { useLocaleStoreWithOut } from '/@/store/modules/locale';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useHeaderSetting } from '/@/hooks/setting/useHeaderSetting';
  import { useUserStore } from '/@/store/modules/user';
  import { useLocale } from '/@/locales/useLocale';
  import { queryRegionCountryApi } from '/@/views/system/depart/depart.api';
  import { getWebconfigUrlListApi } from '/@/api/common/api';
  import { getToken } from '/@/utils/auth';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { AppLocalePicker } from '/@/components/Application';
  import { UserDropDown } from '/@/layouts/default/header/components';
  import logoImg from '/@/assets/images/logo.png';
  import workbenches from '/@/assets/images/workbenches.png';
  import UpdatePassword from './UpdatePassword.vue';
  import { AuthEnum } from '/@/enums/authEnum';

  const { t } = useI18n();
  const { getShowLocalePicker } = useLocale();
  const { prefixCls } = useDesign('layout-header');
  const { getHeaderTheme } = useHeaderSetting();
  const { userInfo } = useUserStore();

  // 获取屏幕高度
  const screenHeight = ref(window.innerHeight); // 减去header的高度

  // 修改密码弹窗是否显示
  const updatePasswordRef = ref();
  const passwordVisible = ref(false);

  const permissionStore = usePermissionStore();

  // 大区以及国家数据
  const regionCountryList = ref([] as any);
  async function getRegionCountryList() {
    try {
      const res = await queryRegionCountryApi();
      regionCountryList.value =
        res?.map((mapItems) => {
          return {
            label: mapItems.region,
            value: mapItems.regionCode,
            children:
              mapItems.countries?.map((mapItem) => {
                return {
                  label: mapItem.regionCountry,
                  value: mapItem.regionCountryCode,
                };
              }) || undefined,
          };
        }) || [];
    } catch (err) {}
  }

  const platformData = ref([] as any[]);

  // platformData.value = permissionStore.getTopMenuList.filter((filterItems) => filterItems.clientId !== '1');
  platformData.value = permissionStore.getTopMenuList;

  const goToLink = (items, dashboardPath = '') => {
    const currentUrl = window.location.origin;
    // 处理看板页面的跳转
    if (dashboardPath) {
      window.open(`${currentUrl}${dashboardPath}`);
      return;
    }
    if (!items.selectSite && items.clientId !== '1') {
      return message.warning(t('common.plase_login_site_text'));
    }
    if (items.clientId === '1') {
      window.open(`${currentUrl}/basic-data`);
    } else {
      const localeStore = useLocaleStoreWithOut();
      const [url, country] = items.selectSite?.split('____') || [];
      const newurl = /^https:\/\//.test(url) ? url : `https://${url}`;
      window.open(`${newurl}?bAccesstoken=${encodeURI(getToken())}&locale=${localeStore.getLocale}&countrycode=${country}`);
    }
  };

  onMounted(async () => {
    // 根据用户信息判定是否第一次登录，如果是第一次登录需要强制修改密码
    // const userStore = useUserStore();
    // userStore.getUserInfo.needChangePwd === 1 && toUpdatePassword();

    await getRegionCountryList();
    getWebconfigUrlListApi().then((res) => {
      if (res?.length > 0) {
        const newRes = res.map((mapItems) => {
          let region_country_value = '';
          region_country_value = region_country_value = regionCountryList.value.reduce((returnVal, nextParams) => {
            if (mapItems.region === nextParams.value) {
              returnVal = returnVal + nextParams.label + '-';
              if (nextParams.children) {
                const findParams = nextParams.children.find((findItems) => findItems.value === mapItems.country);
                if (!!findParams) {
                  returnVal = returnVal + findParams.label;
                }
              }
            }
            return returnVal;
          }, '');
          if (!region_country_value) {
            region_country_value = `${mapItems.region}-${mapItems.country}`;
          }
          return {
            ...mapItems,
            region_country: region_country_value,
          };
        });
        platformData.value.forEach((item) => {
          item.options = [];
          newRes.forEach((resItem) => {
            if (item.clientId == resItem.type) {
              item.options = [
                ...(item.options || []),
                {
                  ...resItem,
                  disabled: !resItem.usable,
                  url: `${resItem.url}____${resItem.country}`,
                },
              ];
            }
          });
          if (item.options?.length === 1) {
            item.selectSite = item.options[0].url;
          } else {
            const findItem = item.options.find((option) => option.country.includes(userInfo?.country));
            if (findItem) {
              item.selectSite = findItem.url;
            } else {
              item.selectSite = null;
            }
          }
        });
        platformData.value = platformData.value.filter((item) => item.options?.length > 0 || item.clientId === '1');
      } else {
        platformData.value = platformData.value.filter((item) => item.clientId === '1');
      }
    });
  });
</script>
<style lang="less" scoped>
  @font-face {
    font-family: 'AlimamaShuHeiTi-Bold';
    src: url('@/assets/fonts/AlimamaShuHeiTi-Bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
  }

  .workbenchesTitle {
    font-family: 'AlimamaShuHeiTi-Bold', 'PingFang SC', Arial, sans-serif;
    font-weight: bold;
    font-size: 74px;
    color: #000000;
    line-height: 84px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .workbenchesDesc {
    font-weight: 400;
    font-size: 18px;
    color: #4b526a;
    line-height: 22px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-top: 2px;
  }

  ::v-deep(.jeecg-page-wrapper-content) {
    margin: 0;
  }

  .ant-card-bordered {
    border: none;
  }

  .platform_content {
    display: flex;
  }

  .groupTitle {
    font-weight: 500;
    font-size: 32px;
    color: #242734;
    line-height: 24px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-top: 3.2%;
    margin-bottom: 32px;
  }

  .every_platform {
    width: 302px;
    margin-right: 38px;
    box-sizing: border-box;
    border-radius: 20px;
    box-shadow:
      2px 2px 8px 0px rgba(82, 90, 102, 0.08),
      1px 1px 2px 0px rgba(82, 90, 102, 0.04);
    margin-bottom: 20px;
    background: #ffffff;
    display: inline-block;

    .content {
      box-sizing: border-box;
      padding: 32px 24px;

      .title {
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #303445;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-bottom: 12px;
      }
    }

    .goto {
      border-top: 1px solid #f2f2f6;
      width: 100%;
      height: 48px;
      line-height: 48px;
      padding-left: 24px;
      font-size: 16px;
      color: #1990ff;
      border-radius: 0 0 20px 20px;
      cursor: pointer;
      position: relative;

      &:hover {
        background: linear-gradient(270deg, #1e91fc 0%, #5bb0ff 100%);
        color: #fff;
      }

      .rightIcon {
        position: absolute;
        right: 24px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 14px;
        color: #fff;
      }
    }
  }
</style>
